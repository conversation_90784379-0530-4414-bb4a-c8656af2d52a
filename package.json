{"name": "chargeup-vn", "version": "1.0.0", "description": "VinFast charging station finder and trip planner for Vietnam", "main": "dist/index.js", "scripts": {"dev": "nodemon src/index.ts", "build": "tsc", "start": "node dist/index.js", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "lint": "eslint src/**/*.ts", "lint:fix": "eslint src/**/*.ts --fix", "format": "prettier --write src/**/*.ts", "db:migrate": "prisma migrate dev", "db:generate": "prisma generate", "db:seed": "ts-node prisma/seed.ts", "docker:build": "docker build -t chargeup-vn .", "docker:run": "docker run -p 3000:3000 chargeup-vn"}, "keywords": ["vinfast", "electric-vehicle", "charging-station", "trip-planner", "vietnam", "mobile-app", "api"], "author": "ChargeUp VN Team", "license": "MIT", "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.1.0", "compression": "^1.7.4", "morgan": "^1.10.0", "dotenv": "^16.3.1", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "express-rate-limit": "^7.1.5", "express-validator": "^7.0.1", "multer": "^1.4.5-lts.1", "sharp": "^0.33.1", "@prisma/client": "^5.7.1", "redis": "^4.6.11", "socket.io": "^4.7.4", "axios": "^1.6.2", "node-cron": "^3.0.3", "winston": "^3.11.0", "joi": "^17.11.0"}, "devDependencies": {"@types/node": "^20.10.5", "@types/express": "^4.17.21", "@types/cors": "^2.8.17", "@types/compression": "^1.7.5", "@types/morgan": "^1.9.9", "@types/bcryptjs": "^2.4.6", "@types/jsonwebtoken": "^9.0.5", "@types/multer": "^1.4.11", "@types/node-cron": "^3.0.11", "@types/jest": "^29.5.8", "@types/supertest": "^2.0.16", "typescript": "^5.3.3", "ts-node": "^10.9.1", "nodemon": "^3.0.2", "jest": "^29.7.0", "ts-jest": "^29.1.1", "supertest": "^6.3.3", "eslint": "^8.55.0", "@typescript-eslint/eslint-plugin": "^6.13.1", "@typescript-eslint/parser": "^6.13.1", "prettier": "^3.1.0", "prisma": "^5.7.1"}, "engines": {"node": ">=20.0.0", "npm": ">=9.0.0"}, "repository": {"type": "git", "url": "https://github.com/your-username/chargeup-vn.git"}, "bugs": {"url": "https://github.com/your-username/chargeup-vn/issues"}, "homepage": "https://github.com/your-username/chargeup-vn#readme"}