import 'package:dio/dio.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:hive/hive.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../config/app_config.dart';
import '../network/api_client.dart';
import '../network/dio_client.dart';
import '../network/network_interceptor.dart';
import '../storage/secure_storage_service.dart';
import '../storage/local_storage_service.dart';
import '../utils/logger.dart';

/// Dependency injection container
/// Initializes and provides all app dependencies
class InjectionContainer {
  InjectionContainer._();

  static final InjectionContainer _instance = InjectionContainer._();
  static InjectionContainer get instance => _instance;

  // Storage
  late final SharedPreferences _sharedPreferences;
  late final FlutterSecureStorage _secureStorage;
  late final Box _hiveBox;

  // Network
  late final Dio _dio;
  late final ApiClient _apiClient;

  // Services
  late final SecureStorageService _secureStorageService;
  late final LocalStorageService _localStorageService;

  // Getters for dependencies
  SharedPreferences get sharedPreferences => _sharedPreferences;
  FlutterSecureStorage get secureStorage => _secureStorage;
  Box get hiveBox => _hiveBox;
  Dio get dio => _dio;
  ApiClient get apiClient => _apiClient;
  SecureStorageService get secureStorageService => _secureStorageService;
  LocalStorageService get localStorageService => _localStorageService;

  /// Initialize all dependencies
  Future<void> init() async {
    AppLogger.info('Initializing dependencies...');

    try {
      // Initialize storage
      await _initStorage();

      // Initialize network
      await _initNetwork();

      // Initialize services
      await _initServices();

      AppLogger.info('Dependencies initialized successfully');
    } catch (e, stackTrace) {
      AppLogger.error('Failed to initialize dependencies', e, stackTrace);
      rethrow;
    }
  }

  /// Initialize storage dependencies
  Future<void> _initStorage() async {
    AppLogger.debug('Initializing storage...');

    // SharedPreferences
    _sharedPreferences = await SharedPreferences.getInstance();

    // Secure Storage
    _secureStorage = const FlutterSecureStorage(
      aOptions: AndroidOptions(
        encryptedSharedPreferences: true,
      ),
      iOptions: IOSOptions(
        accessibility: KeychainAccessibility.first_unlock_this_device,
      ),
    );

    // Hive
    _hiveBox = await Hive.openBox('chargeup_cache');

    AppLogger.debug('Storage initialized');
  }

  /// Initialize network dependencies
  Future<void> _initNetwork() async {
    AppLogger.debug('Initializing network...');

    // Create Dio instance
    _dio = DioClient.createDio();

    // Add interceptors
    _dio.interceptors.addAll([
      NetworkInterceptor(),
      if (AppConfig.enableNetworkLogs) LogInterceptor(
        requestBody: true,
        responseBody: true,
        requestHeader: true,
        responseHeader: false,
        error: true,
        logPrint: (object) => AppLogger.debug(object.toString()),
      ),
    ]);

    // Create API client
    _apiClient = ApiClient(_dio);

    AppLogger.debug('Network initialized');
  }

  /// Initialize service dependencies
  Future<void> _initServices() async {
    AppLogger.debug('Initializing services...');

    // Storage services
    _secureStorageService = SecureStorageService(_secureStorage);
    _localStorageService = LocalStorageService(_sharedPreferences, _hiveBox);

    AppLogger.debug('Services initialized');
  }

  /// Clear all cached data (for logout)
  Future<void> clearCache() async {
    AppLogger.info('Clearing cache...');

    try {
      // Clear Hive cache
      await _hiveBox.clear();

      // Clear SharedPreferences (except app settings)
      final keys = _sharedPreferences.getKeys();
      for (final key in keys) {
        if (!key.startsWith('app_settings_')) {
          await _sharedPreferences.remove(key);
        }
      }

      // Clear secure storage (except biometric settings)
      await _secureStorage.deleteAll(
        aOptions: const AndroidOptions(
          encryptedSharedPreferences: true,
        ),
      );

      AppLogger.info('Cache cleared successfully');
    } catch (e, stackTrace) {
      AppLogger.error('Failed to clear cache', e, stackTrace);
    }
  }

  /// Reset all dependencies (for testing)
  Future<void> reset() async {
    AppLogger.info('Resetting dependencies...');

    try {
      // Close Hive box
      await _hiveBox.close();

      // Clear all data
      await clearCache();

      // Re-initialize
      await init();

      AppLogger.info('Dependencies reset successfully');
    } catch (e, stackTrace) {
      AppLogger.error('Failed to reset dependencies', e, stackTrace);
    }
  }
}

/// Global function to initialize dependencies
Future<void> initializeDependencies() async {
  await InjectionContainer.instance.init();
}

/// Global getters for easy access
SharedPreferences get sharedPreferences => InjectionContainer.instance.sharedPreferences;
FlutterSecureStorage get secureStorage => InjectionContainer.instance.secureStorage;
Box get hiveBox => InjectionContainer.instance.hiveBox;
Dio get dio => InjectionContainer.instance.dio;
ApiClient get apiClient => InjectionContainer.instance.apiClient;
SecureStorageService get secureStorageService => InjectionContainer.instance.secureStorageService;
LocalStorageService get localStorageService => InjectionContainer.instance.localStorageService;
