import 'package:flutter/material.dart';

/// Application color palette
class AppColors {
  AppColors._();

  // VinFast Brand Colors
  static const Color primary = Color(0xFF0066B3); // VinFast Blue
  static const Color primaryVariant = Color(0xFF004A85);
  static const Color secondary = Color(0xFF28A745); // Success Green
  static const Color secondaryVariant = Color(0xFF1E7E34);

  // Status Colors for Charging Stations
  static const Color stationAvailable = Color(0xFF28A745); // Green
  static const Color stationBusy = Color(0xFFFD7E14); // Orange
  static const Color stationOffline = Color(0xFFDC3545); // Red
  static const Color stationUnknown = Color(0xFF6C757D); // Gray

  // Light Theme Colors
  static const Color background = Color(0xFFFFFBFE);
  static const Color surface = Color(0xFFFFFFFF);
  static const Color surfaceVariant = Color(0xFFF3F3F3);
  static const Color onSurface = Color(0xFF1C1B1F);
  static const Color onSurfaceVariant = Color(0xFF49454F);
  static const Color outline = Color(0xFF79747E);
  static const Color outlineVariant = Color(0xFFCAC4D0);

  // Dark Theme Colors
  static const Color darkBackground = Color(0xFF101418);
  static const Color darkSurface = Color(0xFF1D1B20);
  static const Color darkSurfaceVariant = Color(0xFF2B2930);
  static const Color darkOnSurface = Color(0xFFE6E1E5);
  static const Color darkOnSurfaceVariant = Color(0xFFCAC4D0);
  static const Color darkOutline = Color(0xFF938F99);

  // Primary Color Variants
  static const Color onPrimary = Color(0xFFFFFFFF);
  static const Color primaryContainer = Color(0xFFD1E4FF);
  static const Color onPrimaryContainer = Color(0xFF001D36);

  // Secondary Color Variants
  static const Color onSecondary = Color(0xFFFFFFFF);
  static const Color secondaryContainer = Color(0xFFB7F397);
  static const Color onSecondaryContainer = Color(0xFF002204);

  // Error Colors
  static const Color error = Color(0xFFBA1A1A);
  static const Color onError = Color(0xFFFFFFFF);
  static const Color errorContainer = Color(0xFFFFDAD6);
  static const Color onErrorContainer = Color(0xFF410002);

  // Warning Colors
  static const Color warning = Color(0xFFF57C00);
  static const Color onWarning = Color(0xFFFFFFFF);
  static const Color warningContainer = Color(0xFFFFE0B2);
  static const Color onWarningContainer = Color(0xFF3E2723);

  // Info Colors
  static const Color info = Color(0xFF2196F3);
  static const Color onInfo = Color(0xFFFFFFFF);
  static const Color infoContainer = Color(0xFFBBDEFB);
  static const Color onInfoContainer = Color(0xFF0D47A1);

  // Neutral Colors
  static const Color neutral = Color(0xFF5F5F5F);
  static const Color neutralVariant = Color(0xFF8E8E8E);
  static const Color onNeutral = Color(0xFFFFFFFF);

  // Map-specific Colors
  static const Color mapBackground = Color(0xFFF5F5F5);
  static const Color mapWater = Color(0xFFAAD3DF);
  static const Color mapRoad = Color(0xFFFFFFFF);
  static const Color mapBuilding = Color(0xFFE0E0E0);
  static const Color mapPark = Color(0xFFC8E6C9);

  // Gradient Colors
  static const LinearGradient primaryGradient = LinearGradient(
    colors: [primary, primaryVariant],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );

  static const LinearGradient successGradient = LinearGradient(
    colors: [secondary, secondaryVariant],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );

  // Shadow Colors
  static const Color shadowLight = Color(0x1A000000);
  static const Color shadowMedium = Color(0x33000000);
  static const Color shadowDark = Color(0x4D000000);

  // Overlay Colors
  static const Color overlayLight = Color(0x0A000000);
  static const Color overlayMedium = Color(0x1F000000);
  static const Color overlayDark = Color(0x3D000000);

  // Material Swatch for Primary Color
  static const MaterialColor primarySwatch = MaterialColor(
    0xFF0066B3,
    <int, Color>{
      50: Color(0xFFE3F2FD),
      100: Color(0xFFBBDEFB),
      200: Color(0xFF90CAF9),
      300: Color(0xFF64B5F6),
      400: Color(0xFF42A5F5),
      500: Color(0xFF0066B3), // Primary
      600: Color(0xFF1E88E5),
      700: Color(0xFF1976D2),
      800: Color(0xFF1565C0),
      900: Color(0xFF0D47A1),
    },
  );

  // Charging Connector Type Colors
  static const Color connectorAC = Color(0xFF4CAF50); // Green for AC
  static const Color connectorDC = Color(0xFF2196F3); // Blue for DC
  static const Color connectorFast = Color(0xFFFF9800); // Orange for Fast DC
  static const Color connectorRapid = Color(0xFF9C27B0); // Purple for Rapid DC

  // Power Level Colors
  static const Color powerLow = Color(0xFF4CAF50); // Green (< 22kW)
  static const Color powerMedium = Color(0xFFFF9800); // Orange (22-50kW)
  static const Color powerHigh = Color(0xFFFF5722); // Red Orange (50-150kW)
  static const Color powerUltra = Color(0xFF9C27B0); // Purple (> 150kW)

  // Utility Methods
  static Color withOpacity(Color color, double opacity) {
    return color.withOpacity(opacity);
  }

  static Color darken(Color color, [double amount = 0.1]) {
    assert(amount >= 0 && amount <= 1);
    final hsl = HSLColor.fromColor(color);
    final hslDark = hsl.withLightness((hsl.lightness - amount).clamp(0.0, 1.0));
    return hslDark.toColor();
  }

  static Color lighten(Color color, [double amount = 0.1]) {
    assert(amount >= 0 && amount <= 1);
    final hsl = HSLColor.fromColor(color);
    final hslLight = hsl.withLightness((hsl.lightness + amount).clamp(0.0, 1.0));
    return hslLight.toColor();
  }

  // Station Status Color Helper
  static Color getStationStatusColor(String status) {
    switch (status.toLowerCase()) {
      case 'available':
        return stationAvailable;
      case 'busy':
      case 'occupied':
        return stationBusy;
      case 'offline':
      case 'maintenance':
        return stationOffline;
      default:
        return stationUnknown;
    }
  }

  // Power Level Color Helper
  static Color getPowerLevelColor(double powerKw) {
    if (powerKw < 22) return powerLow;
    if (powerKw < 50) return powerMedium;
    if (powerKw < 150) return powerHigh;
    return powerUltra;
  }

  // Connector Type Color Helper
  static Color getConnectorTypeColor(String type) {
    switch (type.toLowerCase()) {
      case 'ac':
        return connectorAC;
      case 'dc':
        return connectorDC;
      case 'fast':
        return connectorFast;
      case 'rapid':
        return connectorRapid;
      default:
        return neutral;
    }
  }
}
