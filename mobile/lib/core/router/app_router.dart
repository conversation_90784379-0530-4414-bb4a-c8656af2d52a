import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';

import '../../features/auth/presentation/screens/login_screen.dart';
import '../../features/auth/presentation/screens/register_screen.dart';
import '../../features/auth/presentation/screens/profile_screen.dart';
import '../../features/map/presentation/screens/map_screen.dart';
import '../../features/stations/presentation/screens/station_detail_screen.dart';
import '../../features/trip_planning/presentation/screens/trip_planning_screen.dart';
import '../../features/community/presentation/screens/community_screen.dart';
import '../../shared/presentation/screens/main_screen.dart';
import '../../shared/presentation/screens/splash_screen.dart';
import '../utils/logger.dart';

/// App router configuration using GoRouter
final appRouterProvider = Provider<GoRouter>((ref) {
  return GoRouter(
    initialLocation: '/splash',
    debugLogDiagnostics: true,
    observers: [AppRouterObserver()],
    routes: [
      // Splash Screen
      GoRoute(
        path: '/splash',
        name: 'splash',
        builder: (context, state) => const SplashScreen(),
      ),

      // Authentication Routes
      GoRoute(
        path: '/login',
        name: 'login',
        builder: (context, state) => const LoginScreen(),
      ),
      GoRoute(
        path: '/register',
        name: 'register',
        builder: (context, state) => const RegisterScreen(),
      ),

      // Main App Shell with Bottom Navigation
      ShellRoute(
        builder: (context, state, child) => MainScreen(child: child),
        routes: [
          // Map Tab
          GoRoute(
            path: '/map',
            name: 'map',
            builder: (context, state) => const MapScreen(),
            routes: [
              // Station Detail
              GoRoute(
                path: '/station/:stationId',
                name: 'station-detail',
                builder: (context, state) {
                  final stationId = state.pathParameters['stationId']!;
                  return StationDetailScreen(stationId: stationId);
                },
              ),
            ],
          ),

          // Trip Planning Tab
          GoRoute(
            path: '/trips',
            name: 'trips',
            builder: (context, state) => const TripPlanningScreen(),
          ),

          // Community Tab
          GoRoute(
            path: '/community',
            name: 'community',
            builder: (context, state) => const CommunityScreen(),
          ),

          // Profile Tab
          GoRoute(
            path: '/profile',
            name: 'profile',
            builder: (context, state) => const ProfileScreen(),
          ),
        ],
      ),

      // Standalone Station Detail (for deep links)
      GoRoute(
        path: '/station/:stationId',
        name: 'station-detail-standalone',
        builder: (context, state) {
          final stationId = state.pathParameters['stationId']!;
          return StationDetailScreen(stationId: stationId);
        },
      ),
    ],

    // Error handling
    errorBuilder: (context, state) => ErrorScreen(error: state.error),

    // Redirect logic for authentication
    redirect: (context, state) {
      // TODO: Implement authentication check
      // final isAuthenticated = ref.read(authStateProvider).isAuthenticated;
      // final isOnAuthPage = state.location.startsWith('/login') || 
      //                     state.location.startsWith('/register');
      // final isOnSplash = state.location == '/splash';

      // if (!isAuthenticated && !isOnAuthPage && !isOnSplash) {
      //   return '/login';
      // }

      // if (isAuthenticated && isOnAuthPage) {
      //   return '/map';
      // }

      return null; // No redirect needed
    },
  );
});

/// Router observer for logging navigation events
class AppRouterObserver extends NavigatorObserver {
  @override
  void didPush(Route<dynamic> route, Route<dynamic>? previousRoute) {
    AppLogger.info('Navigation: Pushed ${route.settings.name}');
    super.didPush(route, previousRoute);
  }

  @override
  void didPop(Route<dynamic> route, Route<dynamic>? previousRoute) {
    AppLogger.info('Navigation: Popped ${route.settings.name}');
    super.didPop(route, previousRoute);
  }

  @override
  void didReplace({Route<dynamic>? newRoute, Route<dynamic>? oldRoute}) {
    AppLogger.info('Navigation: Replaced ${oldRoute?.settings.name} with ${newRoute?.settings.name}');
    super.didReplace(newRoute: newRoute, oldRoute: oldRoute);
  }
}

/// Error screen for navigation errors
class ErrorScreen extends StatelessWidget {
  const ErrorScreen({
    super.key,
    required this.error,
  });

  final Exception? error;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Lỗi'),
      ),
      body: Center(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Icon(
                Icons.error_outline,
                size: 64,
                color: Colors.red,
              ),
              const SizedBox(height: 16),
              const Text(
                'Đã xảy ra lỗi',
                style: TextStyle(
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 8),
              Text(
                error?.toString() ?? 'Lỗi không xác định',
                textAlign: TextAlign.center,
                style: const TextStyle(fontSize: 16),
              ),
              const SizedBox(height: 24),
              ElevatedButton(
                onPressed: () => context.go('/map'),
                child: const Text('Về trang chủ'),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

/// Route names for type-safe navigation
class AppRoutes {
  AppRoutes._();

  static const String splash = '/splash';
  static const String login = '/login';
  static const String register = '/register';
  static const String map = '/map';
  static const String trips = '/trips';
  static const String community = '/community';
  static const String profile = '/profile';
  static const String stationDetail = '/station';
}

/// Extension for easy navigation
extension GoRouterExtension on GoRouter {
  void pushAndClearStack(String location) {
    while (canPop()) {
      pop();
    }
    pushReplacement(location);
  }
}

/// Navigation helper methods
class AppNavigation {
  AppNavigation._();

  static void toLogin(BuildContext context) {
    context.go(AppRoutes.login);
  }

  static void toRegister(BuildContext context) {
    context.go(AppRoutes.register);
  }

  static void toMap(BuildContext context) {
    context.go(AppRoutes.map);
  }

  static void toStationDetail(BuildContext context, String stationId) {
    context.push('${AppRoutes.stationDetail}/$stationId');
  }

  static void toProfile(BuildContext context) {
    context.go(AppRoutes.profile);
  }

  static void logout(BuildContext context) {
    // Clear any cached data
    context.go(AppRoutes.login);
  }
}
