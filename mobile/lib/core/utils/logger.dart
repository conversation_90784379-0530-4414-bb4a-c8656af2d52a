import 'dart:developer' as developer;
import 'package:flutter/foundation.dart';
import 'package:logger/logger.dart';

import '../config/app_config.dart';

/// Application logger utility
class AppLogger {
  AppLogger._();

  static late final Logger _logger;
  static bool _initialized = false;

  /// Initialize the logger
  static void init() {
    if (_initialized) return;

    _logger = Logger(
      filter: _AppLogFilter(),
      printer: _AppLogPrinter(),
      output: _AppLogOutput(),
      level: _getLogLevel(),
    );

    _initialized = true;
    info('Logger initialized');
  }

  /// Get log level based on app configuration
  static Level _getLogLevel() {
    if (AppConfig.isProduction) {
      return Level.warning;
    } else if (AppConfig.isStaging) {
      return Level.info;
    } else {
      return Level.debug;
    }
  }

  /// Log debug message
  static void debug(dynamic message, [dynamic error, StackTrace? stackTrace]) {
    if (!_initialized) init();
    _logger.d(message, error: error, stackTrace: stackTrace);
  }

  /// Log info message
  static void info(dynamic message, [dynamic error, StackTrace? stackTrace]) {
    if (!_initialized) init();
    _logger.i(message, error: error, stackTrace: stackTrace);
  }

  /// Log warning message
  static void warning(dynamic message, [dynamic error, StackTrace? stackTrace]) {
    if (!_initialized) init();
    _logger.w(message, error: error, stackTrace: stackTrace);
  }

  /// Log error message
  static void error(dynamic message, [dynamic error, StackTrace? stackTrace]) {
    if (!_initialized) init();
    _logger.e(message, error: error, stackTrace: stackTrace);
  }

  /// Log fatal error message
  static void fatal(dynamic message, [dynamic error, StackTrace? stackTrace]) {
    if (!_initialized) init();
    _logger.f(message, error: error, stackTrace: stackTrace);
  }

  /// Log network request
  static void network(String method, String url, {
    Map<String, dynamic>? headers,
    dynamic body,
    int? statusCode,
    dynamic response,
  }) {
    if (!AppConfig.enableNetworkLogs) return;
    
    final message = StringBuffer();
    message.writeln('🌐 Network Request');
    message.writeln('Method: $method');
    message.writeln('URL: $url');
    
    if (headers != null && headers.isNotEmpty) {
      message.writeln('Headers: $headers');
    }
    
    if (body != null) {
      message.writeln('Body: $body');
    }
    
    if (statusCode != null) {
      message.writeln('Status: $statusCode');
    }
    
    if (response != null) {
      message.writeln('Response: $response');
    }
    
    debug(message.toString());
  }

  /// Log performance metrics
  static void performance(String operation, Duration duration, {
    Map<String, dynamic>? metadata,
  }) {
    if (!AppConfig.enablePerformanceLogs) return;
    
    final message = StringBuffer();
    message.writeln('⚡ Performance');
    message.writeln('Operation: $operation');
    message.writeln('Duration: ${duration.inMilliseconds}ms');
    
    if (metadata != null && metadata.isNotEmpty) {
      message.writeln('Metadata: $metadata');
    }
    
    debug(message.toString());
  }

  /// Log user action
  static void userAction(String action, {
    Map<String, dynamic>? parameters,
  }) {
    final message = StringBuffer();
    message.writeln('👤 User Action');
    message.writeln('Action: $action');
    
    if (parameters != null && parameters.isNotEmpty) {
      message.writeln('Parameters: $parameters');
    }
    
    info(message.toString());
  }
}

/// Custom log filter
class _AppLogFilter extends LogFilter {
  @override
  bool shouldLog(LogEvent event) {
    // Always log in debug mode
    if (kDebugMode) return true;
    
    // In production, only log warnings and errors
    if (AppConfig.isProduction) {
      return event.level.index >= Level.warning.index;
    }
    
    return true;
  }
}

/// Custom log printer
class _AppLogPrinter extends LogPrinter {
  static final Map<Level, String> _levelEmojis = {
    Level.debug: '🐛',
    Level.info: 'ℹ️',
    Level.warning: '⚠️',
    Level.error: '❌',
    Level.fatal: '💀',
  };

  static final Map<Level, String> _levelNames = {
    Level.debug: 'DEBUG',
    Level.info: 'INFO',
    Level.warning: 'WARN',
    Level.error: 'ERROR',
    Level.fatal: 'FATAL',
  };

  @override
  List<String> log(LogEvent event) {
    final emoji = _levelEmojis[event.level] ?? '';
    final levelName = _levelNames[event.level] ?? 'UNKNOWN';
    final timestamp = DateTime.now().toIso8601String();
    
    final lines = <String>[];
    
    // Main log line
    lines.add('$emoji [$levelName] $timestamp: ${event.message}');
    
    // Error details
    if (event.error != null) {
      lines.add('Error: ${event.error}');
    }
    
    // Stack trace (only for errors and in debug mode)
    if (event.stackTrace != null && 
        (event.level.index >= Level.error.index || kDebugMode)) {
      lines.addAll(event.stackTrace.toString().split('\n'));
    }
    
    return lines;
  }
}

/// Custom log output
class _AppLogOutput extends LogOutput {
  @override
  void output(OutputEvent event) {
    for (final line in event.lines) {
      if (kDebugMode) {
        // Use developer.log in debug mode for better IDE integration
        developer.log(
          line,
          name: 'ChargeUp',
          level: _getLogLevel(event.level),
        );
      } else {
        // Use print in release mode
        print(line);
      }
    }
  }

  int _getLogLevel(Level level) {
    switch (level) {
      case Level.debug:
        return 500;
      case Level.info:
        return 800;
      case Level.warning:
        return 900;
      case Level.error:
        return 1000;
      case Level.fatal:
        return 1200;
      default:
        return 800;
    }
  }
}

/// Extension for easy logging in classes
extension LoggerExtension on Object {
  void logDebug(dynamic message, [dynamic error, StackTrace? stackTrace]) {
    AppLogger.debug('${runtimeType}: $message', error, stackTrace);
  }

  void logInfo(dynamic message, [dynamic error, StackTrace? stackTrace]) {
    AppLogger.info('${runtimeType}: $message', error, stackTrace);
  }

  void logWarning(dynamic message, [dynamic error, StackTrace? stackTrace]) {
    AppLogger.warning('${runtimeType}: $message', error, stackTrace);
  }

  void logError(dynamic message, [dynamic error, StackTrace? stackTrace]) {
    AppLogger.error('${runtimeType}: $message', error, stackTrace);
  }
}
