/// Application configuration constants
class AppConfig {
  AppConfig._();
  
  // App Information
  static const String appName = 'ChargeUp VN';
  static const String appVersion = '1.0.0';
  static const String appBuildNumber = '1';
  
  // API Configuration
  static const String baseUrl = String.fromEnvironment(
    'BASE_URL',
    defaultValue: 'http://localhost:3000/api/v1',
  );
  
  static const String websocketUrl = String.fromEnvironment(
    'WEBSOCKET_URL', 
    defaultValue: 'ws://localhost:3001',
  );
  
  // Google Maps API Key
  static const String googleMapsApiKey = String.fromEnvironment(
    'GOOGLE_MAPS_API_KEY',
    defaultValue: '',
  );
  
  // Feature Flags
  static const bool enableAnalytics = bool.fromEnvironment(
    'ENABLE_ANALYTICS',
    defaultValue: false,
  );
  
  static const bool enableCrashlytics = bool.fromEnvironment(
    'ENABLE_CRASHLYTICS',
    defaultValue: false,
  );
  
  static const bool enablePushNotifications = bool.fromEnvironment(
    'ENABLE_PUSH_NOTIFICATIONS',
    defaultValue: false,
  );
  
  // App Settings
  static const int requestTimeoutSeconds = 30;
  static const int maxRetryAttempts = 3;
  static const int cacheExpirationMinutes = 5;
  
  // Map Settings
  static const double defaultMapZoom = 15.0;
  static const double maxMapZoom = 20.0;
  static const double minMapZoom = 10.0;
  static const double defaultSearchRadius = 10.0; // km
  static const int maxStationsPerRequest = 100;
  
  // Real-time Updates
  static const int stationStatusUpdateInterval = 30; // seconds
  static const int locationUpdateInterval = 10; // seconds
  
  // UI Constants
  static const double defaultPadding = 16.0;
  static const double smallPadding = 8.0;
  static const double largePadding = 24.0;
  static const double borderRadius = 12.0;
  static const double smallBorderRadius = 8.0;
  
  // Animation Durations
  static const int shortAnimationMs = 200;
  static const int mediumAnimationMs = 300;
  static const int longAnimationMs = 500;
  
  // Storage Keys
  static const String userTokenKey = 'user_token';
  static const String refreshTokenKey = 'refresh_token';
  static const String userProfileKey = 'user_profile';
  static const String appSettingsKey = 'app_settings';
  static const String mapCacheKey = 'map_cache';
  
  // Deep Link Configuration
  static const String deepLinkScheme = 'chargeup';
  static const String deepLinkHost = 'chargeup.vn';
  
  // Social Links
  static const String websiteUrl = 'https://chargeup.vn';
  static const String supportEmail = '<EMAIL>';
  static const String privacyPolicyUrl = 'https://chargeup.vn/privacy';
  static const String termsOfServiceUrl = 'https://chargeup.vn/terms';
  
  // App Store Links
  static const String iosAppStoreUrl = 'https://apps.apple.com/app/chargeup-vn/id123456789';
  static const String androidPlayStoreUrl = 'https://play.google.com/store/apps/details?id=com.chargeup.vn';
  
  // Environment Detection
  static bool get isProduction => const String.fromEnvironment('ENVIRONMENT') == 'production';
  static bool get isDevelopment => const String.fromEnvironment('ENVIRONMENT') == 'development';
  static bool get isStaging => const String.fromEnvironment('ENVIRONMENT') == 'staging';
  
  // Debug Settings
  static bool get enableDebugMode => !isProduction;
  static bool get enableNetworkLogs => isDevelopment;
  static bool get enablePerformanceLogs => isDevelopment;
}
