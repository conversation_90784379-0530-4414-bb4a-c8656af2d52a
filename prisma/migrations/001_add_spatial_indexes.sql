-- Add spatial indexes for location-based queries
-- This migration adds PostGIS extension and spatial indexes for better performance

-- Enable PostGIS extension (if not already enabled)
CREATE EXTENSION IF NOT EXISTS postgis;

-- Create spatial index for charging stations location
-- This will significantly improve performance for nearby station queries
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_stations_location 
ON charging_stations USING GIST (
  ST_Point(longitude::double precision, latitude::double precision)
);

-- Create additional indexes for common queries
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_stations_status 
ON charging_stations(status) 
WHERE status = 'ACTIVE';

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_stations_city 
ON charging_stations(city);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_stations_operator 
ON charging_stations(operator);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_connectors_station_status 
ON charging_connectors(station_id, status);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_connectors_type_power 
ON charging_connectors(connector_type, power_kw);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_checkins_station_created 
ON check_ins(station_id, created_at DESC);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_checkins_user_created 
ON check_ins(user_id, created_at DESC);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_trips_user_created 
ON trip_plans(user_id, created_at DESC);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_trips_favorite 
ON trip_plans(user_id, is_favorite) 
WHERE is_favorite = true;

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_users_email_verified 
ON users(email) 
WHERE email_verified = true;

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_vehicles_user_primary 
ON vehicles(user_id, is_primary);

-- Create function for distance calculation
CREATE OR REPLACE FUNCTION calculate_distance(
  lat1 double precision,
  lng1 double precision,
  lat2 double precision,
  lng2 double precision
) RETURNS double precision AS $$
BEGIN
  -- Calculate distance using Haversine formula (returns km)
  RETURN (
    6371 * acos(
      cos(radians(lat1)) * 
      cos(radians(lat2)) * 
      cos(radians(lng2) - radians(lng1)) + 
      sin(radians(lat1)) * 
      sin(radians(lat2))
    )
  );
END;
$$ LANGUAGE plpgsql IMMUTABLE;

-- Create function to find nearby stations
CREATE OR REPLACE FUNCTION find_nearby_stations(
  user_lat double precision,
  user_lng double precision,
  radius_km double precision DEFAULT 10.0,
  max_results integer DEFAULT 50
) RETURNS TABLE (
  id text,
  name text,
  address text,
  latitude numeric,
  longitude numeric,
  distance_km double precision,
  status text,
  total_connectors integer,
  available_connectors integer
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    s.id,
    s.name,
    s.address,
    s.latitude,
    s.longitude,
    calculate_distance(user_lat, user_lng, s.latitude::double precision, s.longitude::double precision) as distance_km,
    s.status::text,
    s.total_connectors,
    s.available_connectors
  FROM charging_stations s
  WHERE 
    s.status = 'ACTIVE'
    AND calculate_distance(user_lat, user_lng, s.latitude::double precision, s.longitude::double precision) <= radius_km
  ORDER BY distance_km
  LIMIT max_results;
END;
$$ LANGUAGE plpgsql;

-- Create function to update station availability
CREATE OR REPLACE FUNCTION update_station_availability()
RETURNS TRIGGER AS $$
BEGIN
  -- Update available connectors count when connector status changes
  UPDATE charging_stations 
  SET 
    available_connectors = (
      SELECT COUNT(*) 
      FROM charging_connectors 
      WHERE station_id = COALESCE(NEW.station_id, OLD.station_id)
      AND status = 'AVAILABLE'
    ),
    last_updated = NOW()
  WHERE id = COALESCE(NEW.station_id, OLD.station_id);
  
  RETURN COALESCE(NEW, OLD);
END;
$$ LANGUAGE plpgsql;

-- Create trigger to automatically update station availability
DROP TRIGGER IF EXISTS trigger_update_station_availability ON charging_connectors;
CREATE TRIGGER trigger_update_station_availability
  AFTER INSERT OR UPDATE OR DELETE ON charging_connectors
  FOR EACH ROW
  EXECUTE FUNCTION update_station_availability();

-- Create function to clean up expired refresh tokens
CREATE OR REPLACE FUNCTION cleanup_expired_tokens()
RETURNS integer AS $$
DECLARE
  deleted_count integer;
BEGIN
  DELETE FROM refresh_tokens 
  WHERE expires_at < NOW();
  
  GET DIAGNOSTICS deleted_count = ROW_COUNT;
  RETURN deleted_count;
END;
$$ LANGUAGE plpgsql;

-- Create function to get user statistics
CREATE OR REPLACE FUNCTION get_user_stats(user_id_param text)
RETURNS TABLE (
  total_trips integer,
  total_checkins integer,
  favorite_trips integer,
  avg_rating numeric
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    (SELECT COUNT(*)::integer FROM trip_plans WHERE user_id = user_id_param),
    (SELECT COUNT(*)::integer FROM check_ins WHERE user_id = user_id_param),
    (SELECT COUNT(*)::integer FROM trip_plans WHERE user_id = user_id_param AND is_favorite = true),
    (SELECT ROUND(AVG(rating), 2) FROM check_ins WHERE user_id = user_id_param AND rating IS NOT NULL)
  ;
END;
$$ LANGUAGE plpgsql;

-- Create function to get station statistics
CREATE OR REPLACE FUNCTION get_station_stats(station_id_param text)
RETURNS TABLE (
  total_checkins integer,
  avg_rating numeric,
  total_connectors integer,
  available_connectors integer,
  utilization_rate numeric
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    (SELECT COUNT(*)::integer FROM check_ins WHERE station_id = station_id_param),
    (SELECT ROUND(AVG(rating), 2) FROM check_ins WHERE station_id = station_id_param AND rating IS NOT NULL),
    s.total_connectors,
    s.available_connectors,
    CASE 
      WHEN s.total_connectors > 0 
      THEN ROUND((s.total_connectors - s.available_connectors)::numeric / s.total_connectors * 100, 2)
      ELSE 0
    END as utilization_rate
  FROM charging_stations s
  WHERE s.id = station_id_param;
END;
$$ LANGUAGE plpgsql;
