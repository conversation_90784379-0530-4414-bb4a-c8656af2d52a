import { PrismaClient } from '@prisma/client';
import bcrypt from 'bcryptjs';

const prisma = new PrismaClient();

/**
 * Sample charging stations data for Vietnam
 */
const sampleStations = [
  {
    name: 'VinFast Landmark 81',
    address: 'Vinhomes Central Park, 720A Điện Biên Phủ, Bình Thạnh, TP.HCM',
    latitude: 10.762622,
    longitude: 106.660172,
    city: 'TP.HCM',
    province: 'TP.HCM',
    totalConnectors: 8,
    availableConnectors: 6,
    operatingHours: {
      monday: { open: '06:00', close: '22:00' },
      tuesday: { open: '06:00', close: '22:00' },
      wednesday: { open: '06:00', close: '22:00' },
      thursday: { open: '06:00', close: '22:00' },
      friday: { open: '06:00', close: '22:00' },
      saturday: { open: '06:00', close: '22:00' },
      sunday: { open: '06:00', close: '22:00' },
    },
    amenities: ['cafe', 'restroom', 'shopping', 'parking'],
    pricing: {
      ac: { price: 3500, unit: 'VND/kWh' },
      dc: { price: 4500, unit: 'VND/kWh' },
    },
    hasParking: true,
    isAccessible: true,
  },
  {
    name: 'VinFast Times City',
    address: '458 Minh Khai, Hai Bà Trưng, Hà Nội',
    latitude: 20.997536,
    longitude: 105.868549,
    city: 'Hà Nội',
    province: 'Hà Nội',
    totalConnectors: 6,
    availableConnectors: 4,
    operatingHours: {
      monday: { open: '24/7' },
      tuesday: { open: '24/7' },
      wednesday: { open: '24/7' },
      thursday: { open: '24/7' },
      friday: { open: '24/7' },
      saturday: { open: '24/7' },
      sunday: { open: '24/7' },
    },
    amenities: ['cafe', 'restroom', 'shopping', 'parking', 'wifi'],
    pricing: {
      ac: { price: 3200, unit: 'VND/kWh' },
      dc: { price: 4200, unit: 'VND/kWh' },
    },
    hasParking: true,
    isAccessible: true,
  },
  {
    name: 'VinFast Đà Nẵng',
    address: '123 Nguyễn Văn Linh, Hải Châu, Đà Nẵng',
    latitude: 16.047079,
    longitude: 108.206230,
    city: 'Đà Nẵng',
    province: 'Đà Nẵng',
    totalConnectors: 4,
    availableConnectors: 3,
    operatingHours: {
      monday: { open: '06:00', close: '23:00' },
      tuesday: { open: '06:00', close: '23:00' },
      wednesday: { open: '06:00', close: '23:00' },
      thursday: { open: '06:00', close: '23:00' },
      friday: { open: '06:00', close: '23:00' },
      saturday: { open: '06:00', close: '23:00' },
      sunday: { open: '06:00', close: '23:00' },
    },
    amenities: ['restroom', 'parking'],
    pricing: {
      ac: { price: 3300, unit: 'VND/kWh' },
      dc: { price: 4300, unit: 'VND/kWh' },
    },
    hasParking: true,
    isAccessible: false,
  },
  {
    name: 'VinFast Cần Thơ',
    address: '456 Trần Hưng Đạo, Ninh Kiều, Cần Thơ',
    latitude: 10.045162,
    longitude: 105.746857,
    city: 'Cần Thơ',
    province: 'Cần Thơ',
    totalConnectors: 6,
    availableConnectors: 5,
    operatingHours: {
      monday: { open: '06:00', close: '22:00' },
      tuesday: { open: '06:00', close: '22:00' },
      wednesday: { open: '06:00', close: '22:00' },
      thursday: { open: '06:00', close: '22:00' },
      friday: { open: '06:00', close: '22:00' },
      saturday: { open: '06:00', close: '22:00' },
      sunday: { open: '06:00', close: '22:00' },
    },
    amenities: ['cafe', 'restroom', 'parking'],
    pricing: {
      ac: { price: 3400, unit: 'VND/kWh' },
      dc: { price: 4400, unit: 'VND/kWh' },
    },
    hasParking: true,
    isAccessible: true,
  },
];

/**
 * Sample connector configurations
 */
const getConnectorsForStation = (totalConnectors: number) => {
  const connectors = [];
  
  // Mix of AC and DC connectors
  for (let i = 1; i <= totalConnectors; i++) {
    if (i <= Math.ceil(totalConnectors / 2)) {
      // AC connectors
      connectors.push({
        connectorNumber: i,
        connectorType: 'AC' as const,
        connectorStandard: 'Type2',
        powerKw: 11,
        status: Math.random() > 0.3 ? 'AVAILABLE' as const : 'OCCUPIED' as const,
      });
    } else {
      // DC connectors
      const powerOptions = [30, 60, 150];
      connectors.push({
        connectorNumber: i,
        connectorType: 'DC' as const,
        connectorStandard: 'CCS2',
        powerKw: powerOptions[Math.floor(Math.random() * powerOptions.length)],
        status: Math.random() > 0.2 ? 'AVAILABLE' as const : 'OCCUPIED' as const,
      });
    }
  }
  
  return connectors;
};

/**
 * Create sample users
 */
async function createSampleUsers() {
  console.log('Creating sample users...');
  
  const passwordHash = await bcrypt.hash('password123', 12);
  
  const users = await Promise.all([
    prisma.user.create({
      data: {
        email: '<EMAIL>',
        name: 'Nguyễn Văn Minh',
        phone: '+84901234567',
        passwordHash,
        emailVerified: true,
        language: 'vi',
        vehicles: {
          create: {
            model: 'VFe34',
            variant: 'Plus',
            year: 2023,
            color: 'Xanh',
            batteryCapacity: 42,
            rangeWltp: 285,
            maxChargingPower: 70,
            isPrimary: true,
            nickname: 'Xe của Minh',
          },
        },
      },
    }),
    prisma.user.create({
      data: {
        email: '<EMAIL>',
        name: 'Trần Thị Lan',
        phone: '+84907654321',
        passwordHash,
        emailVerified: true,
        language: 'vi',
        vehicles: {
          create: {
            model: 'VF8',
            variant: 'Plus',
            year: 2023,
            color: 'Trắng',
            batteryCapacity: 87.7,
            rangeWltp: 420,
            maxChargingPower: 150,
            isPrimary: true,
            nickname: 'VF8 gia đình',
          },
        },
      },
    }),
  ]);
  
  console.log(`Created ${users.length} sample users`);
  return users;
}

/**
 * Create sample charging stations
 */
async function createSampleStations() {
  console.log('Creating sample charging stations...');
  
  const stations = [];
  
  for (const stationData of sampleStations) {
    const station = await prisma.chargingStation.create({
      data: {
        ...stationData,
        connectors: {
          create: getConnectorsForStation(stationData.totalConnectors),
        },
      },
    });
    
    stations.push(station);
  }
  
  console.log(`Created ${stations.length} sample charging stations`);
  return stations;
}

/**
 * Create sample check-ins
 */
async function createSampleCheckIns(users: any[], stations: any[]) {
  console.log('Creating sample check-ins...');
  
  const checkIns = [];
  
  // Create some random check-ins
  for (let i = 0; i < 10; i++) {
    const user = users[Math.floor(Math.random() * users.length)];
    const station = stations[Math.floor(Math.random() * stations.length)];
    
    const checkIn = await prisma.checkIn.create({
      data: {
        userId: user.id,
        stationId: station.id,
        rating: Math.floor(Math.random() * 5) + 1,
        review: [
          'Trạm sạc rất tốt, sạch sẽ và tiện lợi!',
          'Sạc nhanh, nhân viên thân thiện.',
          'Vị trí thuận tiện, có nhiều tiện ích xung quanh.',
          'Trạm hoạt động ổn định, giá cả hợp lý.',
          'Cần cải thiện thêm về không gian chờ.',
        ][Math.floor(Math.random() * 5)],
        connectorStatus: {
          connector1: 'working',
          connector2: 'working',
          connector3: 'maintenance',
        },
        isVerified: Math.random() > 0.3,
      },
    });
    
    checkIns.push(checkIn);
  }
  
  console.log(`Created ${checkIns.length} sample check-ins`);
  return checkIns;
}

/**
 * Create sample trip plans
 */
async function createSampleTripPlans(users: any[]) {
  console.log('Creating sample trip plans...');
  
  const tripPlans = [];
  
  const sampleTrips = [
    {
      name: 'Hà Nội - Đà Nẵng',
      originLat: 21.028511,
      originLng: 105.804817,
      originAddress: 'Hà Nội, Việt Nam',
      destinationLat: 16.047079,
      destinationLng: 108.206230,
      destinationAddress: 'Đà Nẵng, Việt Nam',
      totalDistance: 763,
      totalTime: 720, // 12 hours
      totalChargingTime: 90,
      estimatedCost: 850000,
    },
    {
      name: 'TP.HCM - Cần Thơ',
      originLat: 10.762622,
      originLng: 106.660172,
      originAddress: 'TP.HCM, Việt Nam',
      destinationLat: 10.045162,
      destinationLng: 105.746857,
      destinationAddress: 'Cần Thơ, Việt Nam',
      totalDistance: 169,
      totalTime: 180, // 3 hours
      totalChargingTime: 30,
      estimatedCost: 200000,
    },
  ];
  
  for (const tripData of sampleTrips) {
    const user = users[Math.floor(Math.random() * users.length)];
    
    const tripPlan = await prisma.tripPlan.create({
      data: {
        ...tripData,
        userId: user.id,
        vehicleId: user.vehicles?.[0]?.id,
        initialBattery: 80,
        targetBattery: 20,
        isFavorite: Math.random() > 0.5,
      },
    });
    
    tripPlans.push(tripPlan);
  }
  
  console.log(`Created ${tripPlans.length} sample trip plans`);
  return tripPlans;
}

/**
 * Main seed function
 */
async function main() {
  console.log('🌱 Starting database seed...');
  
  try {
    // Clear existing data
    console.log('Clearing existing data...');
    await prisma.checkIn.deleteMany();
    await prisma.tripStop.deleteMany();
    await prisma.tripPlan.deleteMany();
    await prisma.chargingConnector.deleteMany();
    await prisma.chargingStation.deleteMany();
    await prisma.vehicle.deleteMany();
    await prisma.refreshToken.deleteMany();
    await prisma.user.deleteMany();
    
    // Create sample data
    const users = await createSampleUsers();
    const stations = await createSampleStations();
    await createSampleCheckIns(users, stations);
    await createSampleTripPlans(users);
    
    // Create system config
    await prisma.systemConfig.upsert({
      where: { key: 'app_version' },
      update: { value: '1.0.0' },
      create: {
        key: 'app_version',
        value: '1.0.0',
        description: 'Current application version',
        category: 'system',
        isPublic: true,
      },
    });
    
    console.log('✅ Database seed completed successfully!');
    
    // Print summary
    const stats = await Promise.all([
      prisma.user.count(),
      prisma.chargingStation.count(),
      prisma.chargingConnector.count(),
      prisma.tripPlan.count(),
      prisma.checkIn.count(),
    ]);
    
    console.log('\n📊 Database Summary:');
    console.log(`Users: ${stats[0]}`);
    console.log(`Charging Stations: ${stats[1]}`);
    console.log(`Connectors: ${stats[2]}`);
    console.log(`Trip Plans: ${stats[3]}`);
    console.log(`Check-ins: ${stats[4]}`);
    
  } catch (error) {
    console.error('❌ Seed failed:', error);
    throw error;
  }
}

// Run the seed
main()
  .catch((e) => {
    console.error(e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
