// ChargeUp VN Database Schema
// This is your Prisma schema file for ChargeUp VN

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// ============================================================================
// USER MANAGEMENT
// ============================================================================

model User {
  id        String   @id @default(cuid())
  email     String   @unique
  phone     String?  @unique
  name      String
  avatarUrl String?  @map("avatar_url")
  
  // Authentication
  passwordHash String  @map("password_hash")
  emailVerified Boolean @default(false) @map("email_verified")
  phoneVerified Boolean @default(false) @map("phone_verified")
  
  // Profile
  dateOfBirth DateTime? @map("date_of_birth")
  gender      Gender?
  
  // Preferences
  language    String @default("vi")
  units       String @default("metric") // metric or imperial
  theme       String @default("system") // light, dark, system
  
  // Security
  lastLoginAt    DateTime? @map("last_login_at")
  loginAttempts  Int       @default(0) @map("login_attempts")
  lockedUntil    DateTime? @map("locked_until")
  
  // Timestamps
  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @updatedAt @map("updated_at")
  
  // Relations
  vehicles     Vehicle[]
  tripPlans    TripPlan[]
  checkIns     CheckIn[]
  refreshTokens RefreshToken[]
  
  @@map("users")
}

model RefreshToken {
  id        String   @id @default(cuid())
  token     String   @unique
  userId    String   @map("user_id")
  expiresAt DateTime @map("expires_at")
  createdAt DateTime @default(now()) @map("created_at")
  
  // Relations
  user User @relation(fields: [userId], references: [id], onDelete: Cascade)
  
  @@map("refresh_tokens")
}

enum Gender {
  MALE
  FEMALE
  OTHER
  PREFER_NOT_TO_SAY
}

// ============================================================================
// VEHICLE MANAGEMENT
// ============================================================================

model Vehicle {
  id       String @id @default(cuid())
  userId   String @map("user_id")
  
  // Vehicle Details
  model         String // VF5, VF8, VF9, VFe34, etc.
  variant       String? // Plus, Eco, etc.
  year          Int?
  color         String?
  licensePlate  String? @map("license_plate")
  
  // Technical Specifications
  batteryCapacity Int @map("battery_capacity") // kWh
  rangeWltp      Int @map("range_wltp") // km
  maxChargingPower Int @map("max_charging_power") // kW
  
  // Settings
  isPrimary Boolean @default(false) @map("is_primary")
  nickname  String?
  
  // Timestamps
  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @updatedAt @map("updated_at")
  
  // Relations
  user      User       @relation(fields: [userId], references: [id], onDelete: Cascade)
  tripPlans TripPlan[]
  
  @@map("vehicles")
}

// ============================================================================
// CHARGING STATIONS
// ============================================================================

model ChargingStation {
  id   String @id @default(cuid())
  name String
  
  // Location
  address   String
  latitude  Decimal @db.Decimal(10, 8)
  longitude Decimal @db.Decimal(11, 8)
  city      String?
  province  String?
  country   String @default("VN")
  
  // Operator Information
  operator    String @default("VinFast")
  operatorId  String? @map("operator_id")
  
  // Station Details
  totalConnectors     Int @map("total_connectors")
  availableConnectors Int @default(0) @map("available_connectors")
  
  // Status
  status        StationStatus @default(ACTIVE)
  lastUpdated   DateTime      @default(now()) @map("last_updated")
  
  // Operating Information
  operatingHours Json? @map("operating_hours") // Store as JSON
  pricing        Json? // Store pricing info as JSON
  amenities      Json? // Store amenities as JSON array
  
  // Additional Information
  description String?
  website     String?
  phone       String?
  
  // Accessibility
  isAccessible     Boolean @default(false) @map("is_accessible")
  hasParking       Boolean @default(true) @map("has_parking")
  parkingFee       Boolean @default(false) @map("parking_fee")
  
  // Timestamps
  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @updatedAt @map("updated_at")
  
  // Relations
  connectors ChargingConnector[]
  checkIns   CheckIn[]
  tripStops  TripStop[]
  
  @@map("charging_stations")
}

model ChargingConnector {
  id        String @id @default(cuid())
  stationId String @map("station_id")
  
  // Connector Details
  connectorNumber   Int    @map("connector_number")
  connectorType     ConnectorType @map("connector_type")
  connectorStandard String @map("connector_standard") // CCS2, CHAdeMO, Type2
  powerKw          Int    @map("power_kw")
  
  // Status
  status           ConnectorStatus @default(AVAILABLE)
  currentSessionId String?         @map("current_session_id")
  
  // Timestamps
  lastUpdated DateTime @default(now()) @map("last_updated")
  createdAt   DateTime @default(now()) @map("created_at")
  updatedAt   DateTime @updatedAt @map("updated_at")
  
  // Relations
  station ChargingStation @relation(fields: [stationId], references: [id], onDelete: Cascade)
  
  @@unique([stationId, connectorNumber])
  @@map("charging_connectors")
}

enum StationStatus {
  ACTIVE
  INACTIVE
  MAINTENANCE
  OFFLINE
}

enum ConnectorType {
  AC
  DC
}

enum ConnectorStatus {
  AVAILABLE
  OCCUPIED
  OFFLINE
  MAINTENANCE
}

// ============================================================================
// TRIP PLANNING
// ============================================================================

model TripPlan {
  id     String @id @default(cuid())
  userId String @map("user_id")
  
  // Trip Details
  name        String
  description String?
  
  // Route Information
  originLat      Decimal @map("origin_lat") @db.Decimal(10, 8)
  originLng      Decimal @map("origin_lng") @db.Decimal(11, 8)
  originAddress  String  @map("origin_address")
  
  destinationLat     Decimal @map("destination_lat") @db.Decimal(10, 8)
  destinationLng     Decimal @map("destination_lng") @db.Decimal(11, 8)
  destinationAddress String  @map("destination_address")
  
  // Vehicle and Battery
  vehicleId        String? @map("vehicle_id")
  initialBattery   Int     @map("initial_battery") // percentage
  targetBattery    Int     @map("target_battery") // percentage at destination
  
  // Trip Metrics
  totalDistance     Int?     @map("total_distance") // km
  totalTime         Int?     @map("total_time") // minutes
  totalChargingTime Int?     @map("total_charging_time") // minutes
  estimatedCost     Decimal? @map("estimated_cost") @db.Decimal(10, 2)
  
  // Settings
  isFavorite Boolean @default(false) @map("is_favorite")
  isPublic   Boolean @default(false) @map("is_public")
  
  // Route Data
  routeData Json? @map("route_data") // Store detailed route as JSON
  
  // Timestamps
  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @updatedAt @map("updated_at")
  
  // Relations
  user      User       @relation(fields: [userId], references: [id], onDelete: Cascade)
  vehicle   Vehicle?   @relation(fields: [vehicleId], references: [id], onDelete: SetNull)
  tripStops TripStop[]
  
  @@map("trip_plans")
}

model TripStop {
  id         String @id @default(cuid())
  tripPlanId String @map("trip_plan_id")
  stationId  String @map("station_id")
  
  // Stop Details
  stopOrder      Int @map("stop_order")
  arrivalBattery Int @map("arrival_battery") // percentage
  departureBattery Int @map("departure_battery") // percentage
  chargingTime   Int @map("charging_time") // minutes
  
  // Optional waypoint info
  isWaypoint Boolean @default(false) @map("is_waypoint")
  notes      String?
  
  // Timestamps
  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @updatedAt @map("updated_at")
  
  // Relations
  tripPlan TripPlan        @relation(fields: [tripPlanId], references: [id], onDelete: Cascade)
  station  ChargingStation @relation(fields: [stationId], references: [id], onDelete: Cascade)
  
  @@unique([tripPlanId, stopOrder])
  @@map("trip_stops")
}

// ============================================================================
// COMMUNITY FEATURES
// ============================================================================

model CheckIn {
  id        String @id @default(cuid())
  userId    String @map("user_id")
  stationId String @map("station_id")
  
  // Check-in Details
  rating  Int?    // 1-5 stars
  review  String?
  photos  Json?   // Array of photo URLs
  
  // Station Status Report
  connectorStatus Json? @map("connector_status") // Report on each connector
  amenityStatus   Json? @map("amenity_status")   // Report on amenities
  
  // Verification
  isVerified Boolean @default(false) @map("is_verified")
  
  // Timestamps
  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @updatedAt @map("updated_at")
  
  // Relations
  user    User            @relation(fields: [userId], references: [id], onDelete: Cascade)
  station ChargingStation @relation(fields: [stationId], references: [id], onDelete: Cascade)
  
  @@map("check_ins")
}

// ============================================================================
// SYSTEM TABLES
// ============================================================================

model ApiKey {
  id     String @id @default(cuid())
  name   String
  key    String @unique
  
  // Permissions
  permissions Json // Array of permissions
  
  // Usage
  isActive    Boolean @default(true) @map("is_active")
  lastUsedAt  DateTime? @map("last_used_at")
  usageCount  Int @default(0) @map("usage_count")
  
  // Rate Limiting
  rateLimit   Int? @map("rate_limit") // requests per hour
  
  // Timestamps
  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @updatedAt @map("updated_at")
  expiresAt DateTime? @map("expires_at")
  
  @@map("api_keys")
}

model SystemConfig {
  id    String @id @default(cuid())
  key   String @unique
  value Json
  
  // Metadata
  description String?
  category    String?
  isPublic    Boolean @default(false) @map("is_public")
  
  // Timestamps
  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @updatedAt @map("updated_at")
  
  @@map("system_config")
}

// ============================================================================
// INDEXES FOR PERFORMANCE
// ============================================================================

// Spatial index for location-based queries will be added via raw SQL
// CREATE INDEX CONCURRENTLY idx_stations_location ON charging_stations USING GIST (ll_to_earth(latitude, longitude));

// Additional indexes for common queries
// CREATE INDEX CONCURRENTLY idx_stations_status ON charging_stations(status);
// CREATE INDEX CONCURRENTLY idx_connectors_station ON charging_connectors(station_id);
// CREATE INDEX CONCURRENTLY idx_checkins_station ON check_ins(station_id);
// CREATE INDEX CONCURRENTLY idx_checkins_user ON check_ins(user_id);
// CREATE INDEX CONCURRENTLY idx_trips_user ON trip_plans(user_id);
