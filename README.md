# ChargeUp VN 🔋⚡

> **<PERSON>ạ<PERSON> chủ động, <PERSON><PERSON><PERSON> tự tin** - Ứng dụng tìm kiếm và lên kế hoạch sạc xe điện VinFast thông minh nhất tại Việt Nam

[![Flutter](https://img.shields.io/badge/Flutter-3.16+-blue.svg)](https://flutter.dev/)
[![Node.js](https://img.shields.io/badge/Node.js-20+-green.svg)](https://nodejs.org/)
[![TypeScript](https://img.shields.io/badge/TypeScript-5+-blue.svg)](https://www.typescriptlang.org/)
[![License](https://img.shields.io/badge/License-MIT-yellow.svg)](LICENSE)

## 🎯 Tổng Quan

ChargeUp VN là ứng dụng di động toàn diện giúp người dùng xe điện VinFast tại Việt Nam:

- 🗺️ **Tìm kiếm trạm sạc** với thông tin thời gian thực
- 🛣️ **Lập kế hoạch hành trình** thông minh cho chuyến đi dài
- 👥 **Kết nối cộng đồng** người dùng VinFast
- 📱 **Trải nghiệm native** trên iOS và Android

## ✨ Tính Năng Chính

### 🗺️ Bản Đồ Thông Minh
- Hiển thị trạm sạc VinFast với trạng thái thời gian thực
- Tìm kiếm và lọc theo nhiều tiêu chí
- Thông tin chi tiết về từng trạm sạc
- Chỉ đường tích hợp Google Maps

### 🛣️ Lập Kế Hoạch Hành Trình
- Tự động tính toán điểm dừng sạc tối ưu
- Hỗ trợ tất cả dòng xe VinFast (VF 5, VF 8, VF 9, VF e34)
- Ước tính thời gian và chi phí chuyến đi
- Lưu và chia sẻ hành trình yêu thích

### 👥 Cộng Đồng
- Check-in và đánh giá trạm sạc
- Chia sẻ kinh nghiệm và tips
- Cập nhật trạng thái trạm từ cộng đồng
- Bảng xếp hạng người đóng góp

### 👤 Quản Lý Cá Nhân
- Quản lý thông tin xe và hồ sơ
- Lịch sử sử dụng và đánh giá
- Cài đặt thông báo và tùy chỉnh
- Đồng bộ dữ liệu đa thiết bị

## 🏗️ Kiến Trúc Hệ Thống

### Frontend (Mobile App)
- **Framework:** Flutter 3.16+
- **State Management:** Riverpod 2.0
- **Maps:** Google Maps Platform
- **Architecture:** Clean Architecture + MVVM

### Backend (API Services)
- **Runtime:** Node.js 20+ với TypeScript
- **Framework:** Express.js
- **Database:** PostgreSQL + Redis
- **Real-time:** Socket.io + WebSocket

### Infrastructure
- **Cloud:** AWS/GCP
- **Containerization:** Docker + Kubernetes
- **CI/CD:** GitHub Actions
- **Monitoring:** Prometheus + Grafana

## 🚀 Bắt Đầu Nhanh

### Yêu Cầu Hệ Thống
- **Node.js** 20.0.0+
- **Flutter** 3.16.0+
- **PostgreSQL** 15+
- **Redis** 7+
- **Docker** (optional)

### Cài Đặt Backend

```bash
# Clone repository
git clone https://github.com/your-username/chargeup-vn.git
cd chargeup-vn

# Cài đặt dependencies
npm install

# Cấu hình environment variables
cp .env.example .env
# Chỉnh sửa .env với thông tin database và API keys

# Setup database
npm run db:migrate
npm run db:seed

# Khởi chạy development server
npm run dev
```

### Cài Đặt Mobile App

```bash
# Di chuyển vào thư mục mobile
cd mobile

# Cài đặt Flutter dependencies
flutter pub get

# Chạy code generation
flutter packages pub run build_runner build

# Khởi chạy app (iOS)
flutter run -d ios

# Khởi chạy app (Android)
flutter run -d android
```

### Sử Dụng Docker

```bash
# Build và chạy toàn bộ stack
docker-compose up -d

# Chỉ chạy backend services
docker-compose up -d postgres redis api

# Xem logs
docker-compose logs -f api
```

## 📁 Cấu Trúc Dự Án

```
chargeup-vn/
├── docs/                   # Tài liệu dự án
│   ├── prd/               # Product Requirements
│   ├── architecture/      # System Architecture
│   └── api/              # API Documentation
├── src/                   # Backend source code
│   ├── controllers/       # API controllers
│   ├── services/         # Business logic
│   ├── repositories/     # Data access layer
│   ├── models/           # Data models
│   ├── middleware/       # Express middleware
│   ├── routes/           # API routes
│   └── utils/            # Utilities
├── mobile/               # Flutter mobile app
│   ├── lib/
│   │   ├── core/         # Core utilities
│   │   ├── features/     # Feature modules
│   │   ├── shared/       # Shared components
│   │   └── main.dart     # App entry point
│   ├── android/          # Android-specific code
│   ├── ios/              # iOS-specific code
│   └── test/             # Tests
├── prisma/               # Database schema & migrations
├── docker/               # Docker configurations
├── .github/              # GitHub Actions workflows
└── scripts/              # Build & deployment scripts
```

## 🧪 Testing

### Backend Testing
```bash
# Chạy tất cả tests
npm test

# Chạy tests với coverage
npm run test:coverage

# Chạy tests trong watch mode
npm run test:watch
```

### Mobile Testing
```bash
cd mobile

# Unit tests
flutter test

# Integration tests
flutter test integration_test/

# Widget tests
flutter test test/widgets/
```

## 📊 API Documentation

API documentation được tự động generate và có thể truy cập tại:
- **Development:** http://localhost:3000/api/docs
- **Production:** https://api.chargeup.vn/docs

### Các Endpoint Chính

```
GET    /api/v1/stations/nearby          # Tìm trạm sạc gần đây
GET    /api/v1/stations/:id             # Chi tiết trạm sạc
POST   /api/v1/trips/plan               # Lập kế hoạch hành trình
GET    /api/v1/user/profile             # Thông tin người dùng
POST   /api/v1/checkins                 # Check-in tại trạm
GET    /api/v1/community/feed           # Feed cộng đồng
```

## 🔧 Development

### Code Style & Linting
```bash
# Backend
npm run lint          # Check linting
npm run lint:fix      # Fix linting issues
npm run format        # Format code

# Mobile
cd mobile
flutter analyze       # Analyze Dart code
dart format .         # Format Dart code
```

### Database Management
```bash
# Tạo migration mới
npx prisma migrate dev --name add_new_feature

# Reset database
npx prisma migrate reset

# Xem database trong Prisma Studio
npx prisma studio
```

### Environment Variables

Tạo file `.env` từ `.env.example` và cấu hình:

```env
# Database
DATABASE_URL="postgresql://user:password@localhost:5432/chargeup_vn"
REDIS_URL="redis://localhost:6379"

# JWT
JWT_SECRET="your-super-secret-jwt-key"
JWT_REFRESH_SECRET="your-refresh-secret"

# Google Maps
GOOGLE_MAPS_API_KEY="your-google-maps-api-key"

# VinFast API (if available)
VINFAST_API_URL="https://api.vinfast.vn"
VINFAST_API_KEY="your-vinfast-api-key"

# File Storage
AWS_ACCESS_KEY_ID="your-aws-access-key"
AWS_SECRET_ACCESS_KEY="your-aws-secret-key"
AWS_S3_BUCKET="chargeup-vn-uploads"

# Monitoring
SENTRY_DSN="your-sentry-dsn"
```

## 🚀 Deployment

### Production Deployment

```bash
# Build production images
docker build -t chargeup-vn/api:latest .
docker build -t chargeup-vn/mobile:latest ./mobile

# Deploy với Kubernetes
kubectl apply -f k8s/

# Hoặc deploy với Docker Compose
docker-compose -f docker-compose.prod.yml up -d
```

### Mobile App Release

```bash
cd mobile

# Build Android APK
flutter build apk --release

# Build iOS IPA
flutter build ios --release

# Build App Bundle cho Google Play
flutter build appbundle --release
```

## 📈 Monitoring & Analytics

- **Application Performance:** New Relic / DataDog
- **Error Tracking:** Sentry
- **User Analytics:** Firebase Analytics
- **Infrastructure:** Prometheus + Grafana
- **Logs:** ELK Stack

## 🤝 Contributing

Chúng tôi hoan nghênh mọi đóng góp! Vui lòng đọc [CONTRIBUTING.md](CONTRIBUTING.md) để biết thêm chi tiết.

### Development Workflow

1. Fork repository
2. Tạo feature branch (`git checkout -b feature/amazing-feature`)
3. Commit changes (`git commit -m 'Add amazing feature'`)
4. Push to branch (`git push origin feature/amazing-feature`)
5. Tạo Pull Request

## 📄 License

Dự án này được phân phối dưới giấy phép MIT. Xem [LICENSE](LICENSE) để biết thêm chi tiết.

## 📞 Liên Hệ

- **Email:** <EMAIL>
- **Website:** https://chargeup.vn
- **Discord:** [ChargeUp VN Community](https://discord.gg/chargeup-vn)
- **Facebook:** [@ChargeUpVN](https://facebook.com/ChargeUpVN)

## 🙏 Acknowledgments

- [VinFast](https://vinfast.vn) - Cho việc phát triển hệ sinh thái xe điện tại Việt Nam
- [Flutter Team](https://flutter.dev) - Framework tuyệt vời cho mobile development
- [Google Maps Platform](https://developers.google.com/maps) - Dịch vụ bản đồ và định vị
- Cộng đồng open source Việt Nam

---

**Made with ❤️ for the Vietnamese EV community**
