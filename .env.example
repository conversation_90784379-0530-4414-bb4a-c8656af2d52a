# ChargeUp VN - Environment Configuration Template
# Copy this file to .env and fill in your actual values

# =============================================================================
# APPLICATION SETTINGS
# =============================================================================
NODE_ENV=development
PORT=3000
APP_NAME="ChargeUp VN"
APP_VERSION=1.0.0

# =============================================================================
# DATABASE CONFIGURATION
# =============================================================================
# PostgreSQL Database
DATABASE_URL="postgresql://chargeup_user:your_password@localhost:5432/chargeup_vn_dev"
DATABASE_HOST=localhost
DATABASE_PORT=5432
DATABASE_NAME=chargeup_vn_dev
DATABASE_USER=chargeup_user
DATABASE_PASSWORD=your_password

# Redis Cache
REDIS_URL="redis://localhost:6379"
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=0

# =============================================================================
# AUTHENTICATION & SECURITY
# =============================================================================
# JWT Secrets (Generate strong random strings)
JWT_SECRET="your-super-secret-jwt-key-change-this-in-production"
JWT_REFRESH_SECRET="your-refresh-secret-key-change-this-too"
JWT_EXPIRES_IN="15m"
JWT_REFRESH_EXPIRES_IN="7d"

# Password Hashing
BCRYPT_SALT_ROUNDS=12

# Session Secret
SESSION_SECRET="your-session-secret-key"

# =============================================================================
# EXTERNAL API INTEGRATIONS
# =============================================================================
# Google Maps Platform
GOOGLE_MAPS_API_KEY="your-google-maps-api-key"
GOOGLE_PLACES_API_KEY="your-google-places-api-key"

# VinFast API (if available)
VINFAST_API_URL="https://api.vinfast.vn"
VINFAST_API_KEY="your-vinfast-api-key"
VINFAST_API_SECRET="your-vinfast-api-secret"

# Weather API (optional)
OPENWEATHER_API_KEY="your-openweather-api-key"

# =============================================================================
# FILE STORAGE
# =============================================================================
# AWS S3 Configuration
AWS_ACCESS_KEY_ID="your-aws-access-key-id"
AWS_SECRET_ACCESS_KEY="your-aws-secret-access-key"
AWS_REGION="ap-southeast-1"
AWS_S3_BUCKET="chargeup-vn-uploads-dev"

# Alternative: Google Cloud Storage
GCS_PROJECT_ID="your-gcs-project-id"
GCS_BUCKET_NAME="chargeup-vn-uploads-dev"
GCS_KEY_FILE="path/to/service-account-key.json"

# Local file storage (for development)
UPLOAD_DIR="./uploads"
MAX_FILE_SIZE=5242880  # 5MB in bytes

# =============================================================================
# PUSH NOTIFICATIONS
# =============================================================================
# Firebase Cloud Messaging
FCM_SERVER_KEY="your-fcm-server-key"
FCM_PROJECT_ID="your-firebase-project-id"

# Apple Push Notifications (for iOS)
APNS_KEY_ID="your-apns-key-id"
APNS_TEAM_ID="your-apns-team-id"
APNS_BUNDLE_ID="com.chargeup.vn"
APNS_PRIVATE_KEY_PATH="path/to/apns-private-key.p8"
APNS_PRODUCTION=false

# =============================================================================
# EMAIL & SMS SERVICES
# =============================================================================
# SendGrid Email
SENDGRID_API_KEY="your-sendgrid-api-key"
SENDGRID_FROM_EMAIL="<EMAIL>"
SENDGRID_FROM_NAME="ChargeUp VN"

# Twilio SMS
TWILIO_ACCOUNT_SID="your-twilio-account-sid"
TWILIO_AUTH_TOKEN="your-twilio-auth-token"
TWILIO_PHONE_NUMBER="+**********"

# =============================================================================
# MONITORING & ANALYTICS
# =============================================================================
# Sentry Error Tracking
SENTRY_DSN="your-sentry-dsn"
SENTRY_ENVIRONMENT=development

# New Relic APM
NEW_RELIC_LICENSE_KEY="your-new-relic-license-key"
NEW_RELIC_APP_NAME="ChargeUp VN API"

# Google Analytics
GA_TRACKING_ID="UA-XXXXXXXXX-X"

# =============================================================================
# RATE LIMITING
# =============================================================================
# API Rate Limits
RATE_LIMIT_WINDOW_MS=900000  # 15 minutes
RATE_LIMIT_MAX_REQUESTS=100  # requests per window
RATE_LIMIT_SKIP_SUCCESSFUL_REQUESTS=false

# =============================================================================
# CORS CONFIGURATION
# =============================================================================
# Allowed Origins (comma-separated)
CORS_ORIGINS="http://localhost:3000,http://localhost:8080,https://chargeup.vn"
CORS_CREDENTIALS=true

# =============================================================================
# LOGGING
# =============================================================================
LOG_LEVEL=debug
LOG_FORMAT=combined
LOG_FILE_PATH="./logs/app.log"
LOG_MAX_SIZE="20m"
LOG_MAX_FILES="14d"

# =============================================================================
# CACHE CONFIGURATION
# =============================================================================
# Cache TTL (Time To Live) in seconds
CACHE_TTL_STATIONS=300        # 5 minutes
CACHE_TTL_USER_PROFILE=3600   # 1 hour
CACHE_TTL_TRIP_ROUTES=3600    # 1 hour
CACHE_TTL_SEARCH_RESULTS=600  # 10 minutes

# =============================================================================
# REAL-TIME FEATURES
# =============================================================================
# WebSocket Configuration
WEBSOCKET_PORT=3001
WEBSOCKET_CORS_ORIGINS="http://localhost:3000,https://chargeup.vn"

# Socket.io Configuration
SOCKETIO_ADAPTER=redis
SOCKETIO_REDIS_HOST=localhost
SOCKETIO_REDIS_PORT=6379

# =============================================================================
# DEVELOPMENT TOOLS
# =============================================================================
# API Documentation
SWAGGER_ENABLED=true
SWAGGER_PATH="/api/docs"

# Debug Mode
DEBUG_MODE=true
VERBOSE_LOGGING=true

# Mock Data
USE_MOCK_VINFAST_API=true
MOCK_DATA_ENABLED=true

# =============================================================================
# HEALTH CHECKS
# =============================================================================
HEALTH_CHECK_INTERVAL=30000  # 30 seconds
HEALTH_CHECK_TIMEOUT=5000    # 5 seconds

# =============================================================================
# BACKUP & MAINTENANCE
# =============================================================================
# Database Backup
BACKUP_ENABLED=true
BACKUP_SCHEDULE="0 2 * * *"  # Daily at 2 AM
BACKUP_RETENTION_DAYS=30

# Maintenance Mode
MAINTENANCE_MODE=false
MAINTENANCE_MESSAGE="ChargeUp VN is currently under maintenance. Please try again later."

# =============================================================================
# FEATURE FLAGS
# =============================================================================
# Enable/Disable Features
FEATURE_TRIP_PLANNING=true
FEATURE_COMMUNITY=true
FEATURE_PUSH_NOTIFICATIONS=true
FEATURE_PAYMENT_INTEGRATION=false
FEATURE_ANALYTICS=true

# =============================================================================
# MOBILE APP CONFIGURATION
# =============================================================================
# App Store Configuration
IOS_APP_ID="123456789"
ANDROID_PACKAGE_NAME="com.chargeup.vn"

# Deep Linking
DEEP_LINK_SCHEME="chargeup"
DEEP_LINK_HOST="chargeup.vn"

# =============================================================================
# THIRD-PARTY INTEGRATIONS
# =============================================================================
# Social Login
GOOGLE_CLIENT_ID="your-google-client-id"
GOOGLE_CLIENT_SECRET="your-google-client-secret"

FACEBOOK_APP_ID="your-facebook-app-id"
FACEBOOK_APP_SECRET="your-facebook-app-secret"

# Payment Gateway (future)
STRIPE_PUBLISHABLE_KEY="pk_test_..."
STRIPE_SECRET_KEY="sk_test_..."

# =============================================================================
# LOCALIZATION
# =============================================================================
DEFAULT_LANGUAGE="vi"
SUPPORTED_LANGUAGES="vi,en"
TIMEZONE="Asia/Ho_Chi_Minh"

# =============================================================================
# PERFORMANCE TUNING
# =============================================================================
# Database Connection Pool
DB_POOL_MIN=2
DB_POOL_MAX=10
DB_POOL_IDLE_TIMEOUT=30000

# Request Timeout
REQUEST_TIMEOUT=30000  # 30 seconds

# File Upload Limits
MAX_UPLOAD_SIZE=5242880     # 5MB
MAX_UPLOADS_PER_REQUEST=5

# =============================================================================
# SECURITY HEADERS
# =============================================================================
# Content Security Policy
CSP_ENABLED=true
CSP_REPORT_ONLY=false

# HTTPS Configuration
FORCE_HTTPS=false
HSTS_MAX_AGE=31536000  # 1 year

# =============================================================================
# TESTING
# =============================================================================
# Test Database
TEST_DATABASE_URL="postgresql://chargeup_user:your_password@localhost:5432/chargeup_vn_test"

# Test Configuration
TEST_JWT_SECRET="test-jwt-secret"
TEST_TIMEOUT=10000  # 10 seconds
