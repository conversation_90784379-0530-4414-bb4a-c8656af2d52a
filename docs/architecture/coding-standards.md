# ChargeUp VN - Coding Standards & Best Practices

## 📋 General Principles

### Code Quality Standards
1. **Readability First** - Code should be self-documenting
2. **Consistency** - Follow established patterns throughout the codebase
3. **Simplicity** - Prefer simple solutions over complex ones
4. **Performance** - Write efficient code, but prioritize readability
5. **Security** - Always consider security implications
6. **Testability** - Write code that's easy to test

### Documentation Requirements
- All public APIs must have comprehensive documentation
- Complex business logic requires inline comments
- README files for each major module
- Architecture decisions documented in ADRs

## 🎯 Flutter/Dart Standards

### File Organization
```
lib/
├── core/                   # Core utilities and constants
│   ├── constants/         # App constants
│   ├── errors/           # Error handling
│   ├── network/          # Network configuration
│   └── utils/            # Utility functions
├── features/             # Feature-based organization
│   ├── map/
│   │   ├── data/         # Data layer (repositories, models)
│   │   ├── domain/       # Domain layer (entities, use cases)
│   │   └── presentation/ # UI layer (screens, widgets, providers)
│   ├── stations/
│   ├── trip_planning/
│   └── community/
├── shared/               # Shared components
│   ├── widgets/          # Reusable widgets
│   ├── providers/        # Global providers
│   └── models/           # Shared data models
└── main.dart            # App entry point
```

### Naming Conventions
```dart
// Classes: PascalCase
class ChargingStationRepository {}
class UserProfileProvider {}

// Variables and functions: camelCase
String userName = 'John Doe';
void calculateDistance() {}

// Constants: SCREAMING_SNAKE_CASE
const String API_BASE_URL = 'https://api.chargeup.vn';
const int MAX_RETRY_ATTEMPTS = 3;

// Files and directories: snake_case
charging_station_screen.dart
user_profile_widget.dart

// Private members: prefix with underscore
String _privateVariable;
void _privateMethod() {}
```

### Widget Structure
```dart
class ChargingStationCard extends ConsumerWidget {
  const ChargingStationCard({
    super.key,
    required this.station,
    this.onTap,
  });

  final ChargingStation station;
  final VoidCallback? onTap;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Card(
      child: ListTile(
        title: Text(station.name),
        subtitle: Text(station.address),
        trailing: _buildStatusIcon(),
        onTap: onTap,
      ),
    );
  }

  Widget _buildStatusIcon() {
    // Implementation
  }
}
```

### State Management with Riverpod
```dart
// Providers should be global and final
final chargingStationsProvider = StateNotifierProvider<
    ChargingStationsNotifier, 
    AsyncValue<List<ChargingStation>>
>((ref) {
  return ChargingStationsNotifier(
    repository: ref.watch(chargingStationRepositoryProvider),
  );
});

// StateNotifier for complex state
class ChargingStationsNotifier 
    extends StateNotifier<AsyncValue<List<ChargingStation>>> {
  ChargingStationsNotifier({
    required this.repository,
  }) : super(const AsyncValue.loading());

  final ChargingStationRepository repository;

  Future<void> loadStations({
    required double latitude,
    required double longitude,
    double radius = 10.0,
  }) async {
    state = const AsyncValue.loading();
    
    try {
      final stations = await repository.getStationsNearby(
        latitude: latitude,
        longitude: longitude,
        radius: radius,
      );
      state = AsyncValue.data(stations);
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }
}
```

### Error Handling
```dart
// Custom exceptions
class ChargingStationException implements Exception {
  const ChargingStationException(this.message);
  final String message;
  
  @override
  String toString() => 'ChargingStationException: $message';
}

// Result pattern for operations that can fail
sealed class Result<T> {
  const Result();
}

class Success<T> extends Result<T> {
  const Success(this.data);
  final T data;
}

class Failure<T> extends Result<T> {
  const Failure(this.error);
  final Exception error;
}

// Usage
Future<Result<List<ChargingStation>>> getStations() async {
  try {
    final stations = await _apiClient.getStations();
    return Success(stations);
  } catch (e) {
    return Failure(ChargingStationException(e.toString()));
  }
}
```

## 🔧 Backend (Node.js/TypeScript) Standards

### Project Structure
```
src/
├── controllers/          # Request handlers
├── services/            # Business logic
├── repositories/        # Data access layer
├── models/             # Data models/entities
├── middleware/         # Express middleware
├── routes/             # API routes
├── utils/              # Utility functions
├── config/             # Configuration
├── types/              # TypeScript type definitions
└── tests/              # Test files
```

### Naming Conventions
```typescript
// Interfaces: PascalCase with 'I' prefix
interface IChargingStation {
  id: string;
  name: string;
  location: ILocation;
}

// Classes: PascalCase
class ChargingStationService {
  constructor(private repository: IChargingStationRepository) {}
}

// Functions and variables: camelCase
const getUserProfile = async (userId: string): Promise<User> => {
  // Implementation
};

// Constants: SCREAMING_SNAKE_CASE
const MAX_STATIONS_PER_REQUEST = 100;
const DEFAULT_SEARCH_RADIUS = 10;

// Files: kebab-case
charging-station.controller.ts
user-profile.service.ts
```

### API Controller Pattern
```typescript
@Controller('/api/v1/stations')
export class ChargingStationController {
  constructor(
    private readonly stationService: ChargingStationService,
    private readonly logger: Logger
  ) {}

  @Get('/nearby')
  @ValidateQuery(NearbyStationsQueryDto)
  async getNearbyStations(
    @Query() query: NearbyStationsQueryDto,
    @Res() res: Response
  ): Promise<void> {
    try {
      const stations = await this.stationService.findNearby({
        latitude: query.lat,
        longitude: query.lng,
        radius: query.radius || DEFAULT_SEARCH_RADIUS,
      });

      res.status(200).json({
        success: true,
        data: stations,
        meta: {
          count: stations.length,
          timestamp: new Date().toISOString(),
        },
      });
    } catch (error) {
      this.logger.error('Failed to fetch nearby stations', error);
      res.status(500).json({
        success: false,
        error: 'Internal server error',
      });
    }
  }
}
```

### Service Layer Pattern
```typescript
export class ChargingStationService {
  constructor(
    private readonly repository: IChargingStationRepository,
    private readonly cacheService: ICacheService,
    private readonly logger: Logger
  ) {}

  async findNearby(params: NearbyStationsParams): Promise<ChargingStation[]> {
    const cacheKey = `stations:nearby:${params.latitude}:${params.longitude}:${params.radius}`;
    
    // Try cache first
    const cached = await this.cacheService.get<ChargingStation[]>(cacheKey);
    if (cached) {
      this.logger.debug('Returning cached stations');
      return cached;
    }

    // Fetch from database
    const stations = await this.repository.findNearby(params);
    
    // Cache for 5 minutes
    await this.cacheService.set(cacheKey, stations, 300);
    
    return stations;
  }

  async updateStationStatus(
    stationId: string, 
    status: StationStatus
  ): Promise<void> {
    await this.repository.updateStatus(stationId, status);
    
    // Invalidate related caches
    await this.cacheService.deletePattern(`stations:nearby:*`);
    await this.cacheService.delete(`station:${stationId}`);
    
    // Emit real-time update
    this.eventEmitter.emit('station.status.updated', {
      stationId,
      status,
      timestamp: new Date(),
    });
  }
}
```

### Error Handling
```typescript
// Custom error classes
export class AppError extends Error {
  constructor(
    public message: string,
    public statusCode: number = 500,
    public isOperational: boolean = true
  ) {
    super(message);
    this.name = this.constructor.name;
    Error.captureStackTrace(this, this.constructor);
  }
}

export class ValidationError extends AppError {
  constructor(message: string) {
    super(message, 400);
  }
}

export class NotFoundError extends AppError {
  constructor(resource: string) {
    super(`${resource} not found`, 404);
  }
}

// Global error handler middleware
export const errorHandler = (
  error: Error,
  req: Request,
  res: Response,
  next: NextFunction
): void => {
  if (error instanceof AppError) {
    res.status(error.statusCode).json({
      success: false,
      error: error.message,
    });
    return;
  }

  // Log unexpected errors
  logger.error('Unexpected error:', error);
  
  res.status(500).json({
    success: false,
    error: 'Internal server error',
  });
};
```

## 🧪 Testing Standards

### Flutter Testing
```dart
// Unit tests
group('ChargingStationService', () {
  late ChargingStationService service;
  late MockChargingStationRepository mockRepository;

  setUp(() {
    mockRepository = MockChargingStationRepository();
    service = ChargingStationService(repository: mockRepository);
  });

  test('should return nearby stations when repository succeeds', () async {
    // Arrange
    const stations = [
      ChargingStation(id: '1', name: 'Station 1'),
      ChargingStation(id: '2', name: 'Station 2'),
    ];
    when(() => mockRepository.getStationsNearby(any()))
        .thenAnswer((_) async => stations);

    // Act
    final result = await service.getNearbyStations(
      latitude: 10.0,
      longitude: 20.0,
    );

    // Assert
    expect(result, equals(stations));
    verify(() => mockRepository.getStationsNearby(any())).called(1);
  });
});

// Widget tests
testWidgets('ChargingStationCard displays station information', (tester) async {
  // Arrange
  const station = ChargingStation(
    id: '1',
    name: 'Test Station',
    address: '123 Test Street',
  );

  // Act
  await tester.pumpWidget(
    MaterialApp(
      home: ChargingStationCard(station: station),
    ),
  );

  // Assert
  expect(find.text('Test Station'), findsOneWidget);
  expect(find.text('123 Test Street'), findsOneWidget);
});
```

### Backend Testing
```typescript
describe('ChargingStationController', () => {
  let controller: ChargingStationController;
  let service: jest.Mocked<ChargingStationService>;

  beforeEach(() => {
    service = createMockService();
    controller = new ChargingStationController(service, logger);
  });

  describe('GET /nearby', () => {
    it('should return nearby stations', async () => {
      // Arrange
      const mockStations = [
        { id: '1', name: 'Station 1' },
        { id: '2', name: 'Station 2' },
      ];
      service.findNearby.mockResolvedValue(mockStations);

      const req = createMockRequest({
        query: { lat: '10.0', lng: '20.0' },
      });
      const res = createMockResponse();

      // Act
      await controller.getNearbyStations(req.query, res);

      // Assert
      expect(res.status).toHaveBeenCalledWith(200);
      expect(res.json).toHaveBeenCalledWith({
        success: true,
        data: mockStations,
        meta: expect.any(Object),
      });
    });
  });
});
```

## 🔒 Security Standards

### Input Validation
```typescript
// DTO validation with class-validator
export class CreateStationDto {
  @IsString()
  @IsNotEmpty()
  @MaxLength(200)
  name: string;

  @IsString()
  @IsNotEmpty()
  address: string;

  @IsNumber()
  @Min(-90)
  @Max(90)
  latitude: number;

  @IsNumber()
  @Min(-180)
  @Max(180)
  longitude: number;

  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  amenities?: string[];
}
```

### Authentication & Authorization
```typescript
// JWT middleware
export const authenticateToken = (
  req: AuthenticatedRequest,
  res: Response,
  next: NextFunction
): void => {
  const authHeader = req.headers.authorization;
  const token = authHeader?.split(' ')[1];

  if (!token) {
    res.status(401).json({ error: 'Access token required' });
    return;
  }

  try {
    const decoded = jwt.verify(token, process.env.JWT_SECRET!) as JwtPayload;
    req.user = decoded;
    next();
  } catch (error) {
    res.status(403).json({ error: 'Invalid token' });
  }
};

// Role-based authorization
export const requireRole = (roles: UserRole[]) => {
  return (req: AuthenticatedRequest, res: Response, next: NextFunction): void => {
    if (!req.user || !roles.includes(req.user.role)) {
      res.status(403).json({ error: 'Insufficient permissions' });
      return;
    }
    next();
  };
};
```

## 📊 Performance Standards

### Database Queries
```typescript
// Use indexes for frequently queried fields
// Limit result sets
// Use pagination for large datasets
export class ChargingStationRepository {
  async findNearby(params: NearbyStationsParams): Promise<ChargingStation[]> {
    return this.db.chargingStation.findMany({
      where: {
        status: 'active',
        // Use spatial query with index
        location: {
          distance: {
            from: [params.longitude, params.latitude],
            radius: params.radius * 1000, // Convert km to meters
          },
        },
      },
      take: Math.min(params.limit || 50, 100), // Max 100 results
      orderBy: {
        // Order by distance (handled by spatial query)
      },
      include: {
        connectors: {
          where: { status: { not: 'offline' } },
        },
      },
    });
  }
}
```

### Caching Strategy
```typescript
// Cache frequently accessed data
export class CacheService {
  private readonly DEFAULT_TTL = 300; // 5 minutes

  async get<T>(key: string): Promise<T | null> {
    const cached = await this.redis.get(key);
    return cached ? JSON.parse(cached) : null;
  }

  async set<T>(key: string, value: T, ttl: number = this.DEFAULT_TTL): Promise<void> {
    await this.redis.setex(key, ttl, JSON.stringify(value));
  }

  async invalidatePattern(pattern: string): Promise<void> {
    const keys = await this.redis.keys(pattern);
    if (keys.length > 0) {
      await this.redis.del(...keys);
    }
  }
}
```

## 📝 Documentation Standards

### API Documentation
```typescript
/**
 * Get nearby charging stations
 * 
 * @route GET /api/v1/stations/nearby
 * @param {number} lat - Latitude coordinate
 * @param {number} lng - Longitude coordinate
 * @param {number} [radius=10] - Search radius in kilometers
 * @param {number} [limit=50] - Maximum number of results
 * @returns {Promise<ChargingStation[]>} List of nearby charging stations
 * 
 * @example
 * GET /api/v1/stations/nearby?lat=10.762622&lng=106.660172&radius=5
 * 
 * Response:
 * {
 *   "success": true,
 *   "data": [
 *     {
 *       "id": "station-1",
 *       "name": "VinFast Landmark 81",
 *       "address": "Vinhomes Central Park, Bình Thạnh",
 *       "latitude": 10.762622,
 *       "longitude": 106.660172,
 *       "status": "active",
 *       "connectors": [...]
 *     }
 *   ],
 *   "meta": {
 *     "count": 1,
 *     "timestamp": "2025-01-16T10:00:00Z"
 *   }
 * }
 */
```

### Code Comments
```dart
/// Calculates the optimal charging stops for a trip
/// 
/// This algorithm considers:
/// - Vehicle battery capacity and current charge level
/// - Distance between origin and destination
/// - Available charging stations along the route
/// - Charging speed and estimated charging time
/// 
/// Returns a list of [ChargingStop] objects representing
/// the recommended charging locations and durations.
Future<List<ChargingStop>> calculateChargingStops({
  required TripRequest request,
  required VehicleProfile vehicle,
}) async {
  // Implementation details...
}
```

---

These coding standards ensure consistency, maintainability, and quality across the ChargeUp VN codebase. All team members should follow these guidelines and update them as the project evolves.
