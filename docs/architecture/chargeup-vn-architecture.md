# ChargeUp VN - System Architecture Document

## 📋 Document Overview

**Project:** ChargeUp VN - VinFast Charging Station Finder & Trip Planner  
**Version:** 1.0  
**Date:** 2025-01-16  
**Architect:** [Your Name]  
**Status:** Draft  

## 🎯 Architecture Goals

### Primary Objectives
1. **Scalability** - Support 100K+ concurrent users
2. **Real-time Performance** - Sub-second response times
3. **High Availability** - 99.9% uptime
4. **Data Accuracy** - Real-time charging station status
5. **Cross-platform** - iOS and Android native experience

### Non-Functional Requirements
- **Performance:** < 2s map load, < 1s search response
- **Scalability:** Horizontal scaling capability
- **Security:** End-to-end encryption, secure authentication
- **Reliability:** Graceful degradation, offline support
- **Maintainability:** Modular architecture, comprehensive testing

## 🏗️ High-Level Architecture

### System Overview
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Mobile Apps   │    │   Web Dashboard │    │  Admin Portal   │
│  (iOS/Android)  │    │   (Optional)    │    │   (Internal)    │
└─────────┬───────┘    └─────────┬───────┘    └─────────┬───────┘
          │                      │                      │
          └──────────────────────┼──────────────────────┘
                                 │
                    ┌─────────────┴─────────────┐
                    │      API Gateway          │
                    │   (Load Balancer +        │
                    │   Rate Limiting)          │
                    └─────────────┬─────────────┘
                                 │
          ┌──────────────────────┼──────────────────────┐
          │                      │                      │
    ┌─────┴─────┐        ┌───────┴───────┐      ┌───────┴───────┐
    │   Auth    │        │   Core API    │      │   Real-time   │
    │  Service  │        │   Service     │      │   Service     │
    └─────┬─────┘        └───────┬───────┘      └───────┬───────┘
          │                      │                      │
          └──────────────────────┼──────────────────────┘
                                 │
                    ┌─────────────┴─────────────┐
                    │     Data Layer            │
                    │  (PostgreSQL + Redis +    │
                    │   External APIs)          │
                    └───────────────────────────┘
```

## 📱 Frontend Architecture

### Technology Stack
- **Framework:** Flutter 3.16+
- **State Management:** Riverpod 2.0
- **Navigation:** Go Router
- **Maps:** Google Maps Flutter Plugin
- **HTTP Client:** Dio with Interceptors
- **Local Storage:** Hive + Secure Storage
- **Real-time:** WebSocket + Server-Sent Events

### App Architecture Pattern
**Clean Architecture + MVVM**

```
┌─────────────────────────────────────────────────────────┐
│                    Presentation Layer                   │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐     │
│  │   Screens   │  │  Widgets    │  │ ViewModels  │     │
│  │             │  │             │  │ (Riverpod)  │     │
│  └─────────────┘  └─────────────┘  └─────────────┘     │
└─────────────────────┬───────────────────────────────────┘
                      │
┌─────────────────────┴───────────────────────────────────┐
│                   Domain Layer                          │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐     │
│  │  Entities   │  │ Use Cases   │  │ Repositories│     │
│  │             │  │             │  │ (Abstract)  │     │
│  └─────────────┘  └─────────────┘  └─────────────┘     │
└─────────────────────┬───────────────────────────────────┘
                      │
┌─────────────────────┴───────────────────────────────────┐
│                    Data Layer                           │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐     │
│  │ Data Sources│  │   Models    │  │ Repositories│     │
│  │ (API/Local) │  │             │  │ (Concrete)  │     │
│  └─────────────┘  └─────────────┘  └─────────────┘     │
└─────────────────────────────────────────────────────────┘
```

### Key Frontend Components

#### 1. Map Module
```dart
// Core map functionality
- MapScreen (Main map interface)
- StationMarkerWidget (Custom station markers)
- MapController (Map state management)
- LocationService (GPS and location tracking)
- MapStyleProvider (Custom map styling)
```

#### 2. Station Module
```dart
// Station-related features
- StationListScreen (Search results)
- StationDetailScreen (Detailed station info)
- StationFilterWidget (Advanced filtering)
- StationRepository (Data access layer)
- StationService (Business logic)
```

#### 3. Trip Planning Module
```dart
// Trip planning functionality
- TripPlannerScreen (Route planning interface)
- RouteCalculatorService (Algorithm implementation)
- TripTimelineWidget (Visual trip representation)
- VehicleProfileService (Vehicle-specific calculations)
```

#### 4. Community Module
```dart
// Community features
- CheckInScreen (Station check-in)
- ReviewsWidget (User reviews and ratings)
- CommunityFeedScreen (Activity feed)
- UserProfileScreen (User management)
```

## 🔧 Backend Architecture

### Technology Stack
- **Runtime:** Node.js 20+ with TypeScript
- **Framework:** Express.js with Helmet security
- **Database:** PostgreSQL 15+ (Primary) + Redis 7+ (Cache)
- **ORM:** Prisma with connection pooling
- **Authentication:** JWT + Refresh Tokens
- **Real-time:** Socket.io + Server-Sent Events
- **File Storage:** AWS S3 or Google Cloud Storage
- **Monitoring:** Winston logging + Prometheus metrics

### Microservices Architecture

#### 1. API Gateway Service
```typescript
// Entry point for all requests
- Rate limiting (Redis-based)
- Request/Response logging
- Authentication middleware
- Load balancing
- API versioning (/api/v1/)
```

#### 2. Authentication Service
```typescript
// User authentication and authorization
- JWT token generation/validation
- Refresh token rotation
- Password hashing (bcrypt)
- Social login integration (Google, Facebook)
- Role-based access control (RBAC)
```

#### 3. Core API Service
```typescript
// Main business logic
- Station management
- User profile management
- Trip planning algorithms
- Search and filtering
- File upload handling
```

#### 4. Real-time Service
```typescript
// Real-time updates
- WebSocket connections
- Station status broadcasting
- Live user activity
- Push notification triggers
- Event-driven architecture
```

#### 5. Data Sync Service
```typescript
// External data integration
- VinFast API integration
- Scheduled data updates
- Data validation and cleaning
- Backup and recovery
- Analytics data processing
```

### Database Design

#### PostgreSQL Schema

```sql
-- Users table
CREATE TABLE users (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    email VARCHAR(255) UNIQUE NOT NULL,
    phone VARCHAR(20) UNIQUE,
    name VARCHAR(100) NOT NULL,
    avatar_url TEXT,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- Vehicles table (User's VinFast cars)
CREATE TABLE vehicles (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    model VARCHAR(50) NOT NULL, -- VF5, VF8, VF9, VFe34
    variant VARCHAR(50), -- Plus, Eco, etc.
    battery_capacity INTEGER NOT NULL, -- kWh
    range_wltp INTEGER NOT NULL, -- km
    is_primary BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT NOW()
);

-- Charging stations table
CREATE TABLE charging_stations (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(200) NOT NULL,
    address TEXT NOT NULL,
    latitude DECIMAL(10, 8) NOT NULL,
    longitude DECIMAL(11, 8) NOT NULL,
    operator VARCHAR(100) DEFAULT 'VinFast',
    total_connectors INTEGER NOT NULL,
    available_connectors INTEGER DEFAULT 0,
    status VARCHAR(20) DEFAULT 'active', -- active, maintenance, offline
    amenities JSONB, -- cafe, restroom, shopping, etc.
    operating_hours JSONB,
    pricing JSONB,
    last_updated TIMESTAMP DEFAULT NOW(),
    created_at TIMESTAMP DEFAULT NOW()
);

-- Charging connectors table
CREATE TABLE charging_connectors (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    station_id UUID REFERENCES charging_stations(id) ON DELETE CASCADE,
    connector_type VARCHAR(20) NOT NULL, -- AC, DC
    power_kw INTEGER NOT NULL, -- 11, 30, 60, 150, etc.
    connector_standard VARCHAR(20), -- CCS2, CHAdeMO, Type2
    status VARCHAR(20) DEFAULT 'available', -- available, occupied, offline
    current_session_id UUID,
    last_updated TIMESTAMP DEFAULT NOW()
);

-- User check-ins table
CREATE TABLE check_ins (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    station_id UUID REFERENCES charging_stations(id) ON DELETE CASCADE,
    rating INTEGER CHECK (rating >= 1 AND rating <= 5),
    review TEXT,
    photos JSONB, -- Array of photo URLs
    connector_status JSONB, -- Status report for each connector
    created_at TIMESTAMP DEFAULT NOW()
);

-- Trip plans table
CREATE TABLE trip_plans (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    name VARCHAR(200) NOT NULL,
    origin_lat DECIMAL(10, 8) NOT NULL,
    origin_lng DECIMAL(11, 8) NOT NULL,
    destination_lat DECIMAL(10, 8) NOT NULL,
    destination_lng DECIMAL(11, 8) NOT NULL,
    vehicle_id UUID REFERENCES vehicles(id),
    route_data JSONB, -- Detailed route with charging stops
    total_distance INTEGER, -- km
    total_time INTEGER, -- minutes
    total_charging_time INTEGER, -- minutes
    estimated_cost DECIMAL(10, 2), -- VND
    is_favorite BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- Indexes for performance
CREATE INDEX idx_stations_location ON charging_stations USING GIST (
    ll_to_earth(latitude, longitude)
);
CREATE INDEX idx_stations_status ON charging_stations(status);
CREATE INDEX idx_connectors_station ON charging_connectors(station_id);
CREATE INDEX idx_checkins_station ON check_ins(station_id);
CREATE INDEX idx_checkins_user ON check_ins(user_id);
CREATE INDEX idx_trips_user ON trip_plans(user_id);
```

#### Redis Cache Strategy
```typescript
// Cache keys and TTL
const CACHE_KEYS = {
  STATION_STATUS: 'station:status:{stationId}', // TTL: 30s
  STATION_LIST: 'stations:area:{lat}:{lng}:{radius}', // TTL: 5min
  USER_PROFILE: 'user:profile:{userId}', // TTL: 1hour
  TRIP_ROUTE: 'trip:route:{hash}', // TTL: 1hour
  SEARCH_RESULTS: 'search:{query}:{filters}', // TTL: 10min
};
```

## 🔌 External Integrations

### 1. VinFast API Integration
```typescript
// Primary data source for charging stations
interface VinFastAPIClient {
  getStations(): Promise<ChargingStation[]>;
  getStationStatus(stationId: string): Promise<StationStatus>;
  getConnectorStatus(connectorId: string): Promise<ConnectorStatus>;
}

// Fallback: Web scraping with rate limiting
class VinFastScraper {
  private rateLimiter = new RateLimiter(10, 'minute');
  async scrapeStationData(): Promise<StationData[]>;
}
```

### 2. Google Maps Platform
```typescript
// Maps and routing services
interface GoogleMapsService {
  geocoding: GeocodingAPI;
  directions: DirectionsAPI;
  places: PlacesAPI;
  distanceMatrix: DistanceMatrixAPI;
}

// Usage quotas and optimization
const MAPS_CONFIG = {
  dailyQuota: 100000,
  requestsPerSecond: 50,
  cacheTTL: 3600, // 1 hour for routes
};
```

### 3. Push Notifications
```typescript
// Firebase Cloud Messaging
interface NotificationService {
  sendToUser(userId: string, notification: Notification): Promise<void>;
  sendToTopic(topic: string, notification: Notification): Promise<void>;
  scheduleNotification(notification: ScheduledNotification): Promise<void>;
}

// Notification types
enum NotificationType {
  STATION_AVAILABLE = 'station_available',
  TRIP_REMINDER = 'trip_reminder',
  MAINTENANCE_ALERT = 'maintenance_alert',
  COMMUNITY_UPDATE = 'community_update'
}
```

## 🔒 Security Architecture

### Authentication & Authorization
```typescript
// JWT-based authentication
interface AuthTokens {
  accessToken: string; // 15 minutes TTL
  refreshToken: string; // 7 days TTL
  tokenType: 'Bearer';
}

// Role-based permissions
enum UserRole {
  USER = 'user',
  MODERATOR = 'moderator',
  ADMIN = 'admin'
}

// API endpoint protection
const protectedRoutes = {
  '/api/v1/user/*': [UserRole.USER],
  '/api/v1/admin/*': [UserRole.ADMIN],
  '/api/v1/moderate/*': [UserRole.MODERATOR, UserRole.ADMIN]
};
```

### Data Protection
```typescript
// Encryption at rest and in transit
const SECURITY_CONFIG = {
  encryption: {
    algorithm: 'AES-256-GCM',
    keyRotation: '90 days'
  },
  hashing: {
    algorithm: 'bcrypt',
    saltRounds: 12
  },
  tls: {
    version: 'TLSv1.3',
    cipherSuites: ['TLS_AES_256_GCM_SHA384']
  }
};

// Input validation and sanitization
import { body, param, query } from 'express-validator';
import DOMPurify from 'isomorphic-dompurify';
```

## 📊 Monitoring & Analytics

### Application Monitoring
```typescript
// Prometheus metrics
const metrics = {
  httpRequestDuration: new Histogram({
    name: 'http_request_duration_seconds',
    help: 'Duration of HTTP requests in seconds',
    labelNames: ['method', 'route', 'status_code']
  }),
  
  activeUsers: new Gauge({
    name: 'active_users_total',
    help: 'Number of active users'
  }),
  
  stationDataFreshness: new Gauge({
    name: 'station_data_age_seconds',
    help: 'Age of station data in seconds'
  })
};

// Health checks
const healthChecks = {
  database: () => checkPostgreSQLConnection(),
  redis: () => checkRedisConnection(),
  externalAPIs: () => checkVinFastAPI(),
  storage: () => checkS3Connection()
};
```

### User Analytics
```typescript
// Privacy-compliant analytics
interface AnalyticsEvent {
  eventType: string;
  userId?: string; // Hashed for privacy
  sessionId: string;
  timestamp: Date;
  properties: Record<string, any>;
}

// Key metrics tracking
const analyticsEvents = {
  APP_OPENED: 'app_opened',
  STATION_SEARCHED: 'station_searched',
  STATION_SELECTED: 'station_selected',
  TRIP_PLANNED: 'trip_planned',
  CHECK_IN_COMPLETED: 'check_in_completed'
};
```

## 🚀 Deployment Architecture

### Infrastructure Overview
```yaml
# Docker Compose for development
version: '3.8'
services:
  api-gateway:
    image: chargeup/api-gateway:latest
    ports: ["3000:3000"]
    
  auth-service:
    image: chargeup/auth-service:latest
    
  core-api:
    image: chargeup/core-api:latest
    
  realtime-service:
    image: chargeup/realtime-service:latest
    
  postgres:
    image: postgres:15
    environment:
      POSTGRES_DB: chargeup_vn
      
  redis:
    image: redis:7-alpine
```

### Production Deployment (AWS/GCP)
```typescript
// Kubernetes deployment strategy
const deploymentConfig = {
  replicas: {
    apiGateway: 3,
    authService: 2,
    coreAPI: 5,
    realtimeService: 3
  },
  
  resources: {
    requests: { cpu: '100m', memory: '128Mi' },
    limits: { cpu: '500m', memory: '512Mi' }
  },
  
  autoscaling: {
    minReplicas: 2,
    maxReplicas: 20,
    targetCPUUtilization: 70
  }
};
```

### CI/CD Pipeline
```yaml
# GitHub Actions workflow
name: Deploy ChargeUp VN
on:
  push:
    branches: [main, develop]
    
jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - name: Run tests
        run: |
          npm test
          flutter test
          
  build:
    needs: test
    runs-on: ubuntu-latest
    steps:
      - name: Build Docker images
        run: docker build -t chargeup/api:${{ github.sha }} .
        
  deploy:
    needs: build
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main'
    steps:
      - name: Deploy to production
        run: kubectl apply -f k8s/
```

## 📈 Performance Optimization

### Caching Strategy
```typescript
// Multi-level caching
const cachingLayers = {
  L1_Memory: 'In-app cache (Flutter)', // 5MB limit
  L2_Redis: 'Distributed cache', // 1GB limit
  L3_CDN: 'Static assets cache', // Global distribution
  L4_Database: 'Query result cache' // PostgreSQL shared_buffers
};

// Cache invalidation patterns
const invalidationRules = {
  stationStatus: 'Time-based (30s)',
  stationList: 'Event-based + Time-based (5min)',
  userProfile: 'Event-based',
  tripRoutes: 'Time-based (1hour)'
};
```

### Database Optimization
```sql
-- Query optimization examples
-- Spatial index for location-based queries
CREATE INDEX CONCURRENTLY idx_stations_location_gist 
ON charging_stations USING GIST (
  ll_to_earth(latitude, longitude)
);

-- Partial index for active stations only
CREATE INDEX CONCURRENTLY idx_active_stations 
ON charging_stations (latitude, longitude) 
WHERE status = 'active';

-- Composite index for filtered searches
CREATE INDEX CONCURRENTLY idx_stations_search 
ON charging_stations (status, total_connectors, operator);
```

## 🔄 Data Flow Architecture

### Real-time Data Flow
```
VinFast API → Data Sync Service → PostgreSQL → Redis Cache → WebSocket → Mobile App
     ↓              ↓                ↓           ↓            ↓
Community Data → Validation → Database → Cache Update → Push Notification
```

### Trip Planning Algorithm Flow
```typescript
// Simplified trip planning algorithm
class TripPlanningService {
  async calculateOptimalRoute(request: TripRequest): Promise<TripPlan> {
    // 1. Get vehicle specifications
    const vehicle = await this.getVehicleSpecs(request.vehicleId);
    
    // 2. Calculate base route without charging stops
    const baseRoute = await this.mapsService.getDirections(
      request.origin, 
      request.destination
    );
    
    // 3. Identify charging needs based on range
    const chargingNeeds = this.calculateChargingNeeds(
      baseRoute, 
      vehicle, 
      request.initialBattery
    );
    
    // 4. Find optimal charging stations
    const chargingStops = await this.findOptimalStations(
      chargingNeeds,
      request.preferences
    );
    
    // 5. Generate final route with charging stops
    return this.generateFinalRoute(baseRoute, chargingStops);
  }
}
```

---

**Document Status:** Draft v1.0  
**Next Review:** 2025-01-23  
**Stakeholders:** Engineering Team, DevOps Team, Product Team
