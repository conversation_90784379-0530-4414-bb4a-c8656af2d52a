import { z } from 'zod';

/**
 * Environment variables validation schema
 */
const envSchema = z.object({
  // Application
  NODE_ENV: z.enum(['development', 'staging', 'production']).default('development'),
  PORT: z.string().transform(Number).default('3000'),
  APP_NAME: z.string().default('ChargeUp VN API'),
  APP_VERSION: z.string().default('1.0.0'),

  // Database
  DATABASE_URL: z.string(),
  REDIS_URL: z.string().optional(),

  // Authentication
  JWT_SECRET: z.string().min(32),
  JWT_REFRESH_SECRET: z.string().min(32),
  JWT_EXPIRES_IN: z.string().default('15m'),
  JWT_REFRESH_EXPIRES_IN: z.string().default('7d'),

  // External APIs
  GOOGLE_MAPS_API_KEY: z.string().optional(),
  VINFAST_API_URL: z.string().optional(),
  VINFAST_API_KEY: z.string().optional(),

  // File Storage
  AWS_ACCESS_KEY_ID: z.string().optional(),
  AWS_SECRET_ACCESS_KEY: z.string().optional(),
  AWS_REGION: z.string().default('ap-southeast-1'),
  AWS_S3_BUCKET: z.string().optional(),

  // Rate Limiting
  RATE_LIMIT_WINDOW_MS: z.string().transform(Number).default('900000'), // 15 minutes
  RATE_LIMIT_MAX_REQUESTS: z.string().transform(Number).default('100'),

  // CORS
  CORS_ORIGINS: z.string().default('http://localhost:3000'),
  CORS_CREDENTIALS: z.string().transform(Boolean).default('true'),

  // Logging
  LOG_LEVEL: z.enum(['error', 'warn', 'info', 'debug']).default('info'),

  // Cache
  CACHE_TTL_STATIONS: z.string().transform(Number).default('300'), // 5 minutes
  CACHE_TTL_USER_PROFILE: z.string().transform(Number).default('3600'), // 1 hour
});

/**
 * Validate and parse environment variables
 */
const parseEnv = () => {
  try {
    return envSchema.parse(process.env);
  } catch (error) {
    console.error('❌ Invalid environment variables:');
    if (error instanceof z.ZodError) {
      error.errors.forEach((err) => {
        console.error(`  ${err.path.join('.')}: ${err.message}`);
      });
    }
    process.exit(1);
  }
};

const env = parseEnv();

/**
 * Application configuration
 */
export const config = {
  // Application settings
  env: env.NODE_ENV,
  port: env.PORT,
  app: {
    name: env.APP_NAME,
    version: env.APP_VERSION,
  },

  // Database configuration
  database: {
    url: env.DATABASE_URL,
    redis: {
      url: env.REDIS_URL || 'redis://localhost:6379',
    },
  },

  // Authentication configuration
  auth: {
    jwt: {
      secret: env.JWT_SECRET,
      refreshSecret: env.JWT_REFRESH_SECRET,
      expiresIn: env.JWT_EXPIRES_IN,
      refreshExpiresIn: env.JWT_REFRESH_EXPIRES_IN,
    },
    bcrypt: {
      saltRounds: 12,
    },
  },

  // External API configuration
  externalApis: {
    googleMaps: {
      apiKey: env.GOOGLE_MAPS_API_KEY,
    },
    vinfast: {
      baseUrl: env.VINFAST_API_URL,
      apiKey: env.VINFAST_API_KEY,
    },
  },

  // File storage configuration
  storage: {
    aws: {
      accessKeyId: env.AWS_ACCESS_KEY_ID,
      secretAccessKey: env.AWS_SECRET_ACCESS_KEY,
      region: env.AWS_REGION,
      bucket: env.AWS_S3_BUCKET,
    },
    local: {
      uploadDir: './uploads',
      maxFileSize: 5 * 1024 * 1024, // 5MB
    },
  },

  // Rate limiting configuration
  rateLimit: {
    windowMs: env.RATE_LIMIT_WINDOW_MS,
    maxRequests: env.RATE_LIMIT_MAX_REQUESTS,
  },

  // CORS configuration
  cors: {
    origins: env.CORS_ORIGINS.split(',').map(origin => origin.trim()),
    credentials: env.CORS_CREDENTIALS,
  },

  // Logging configuration
  logging: {
    level: env.LOG_LEVEL,
  },

  // Cache configuration
  cache: {
    ttl: {
      stations: env.CACHE_TTL_STATIONS,
      userProfile: env.CACHE_TTL_USER_PROFILE,
    },
  },

  // Feature flags
  features: {
    enableAnalytics: env.NODE_ENV === 'production',
    enableMetrics: true,
    enableSwagger: env.NODE_ENV !== 'production',
  },

  // API configuration
  api: {
    prefix: '/api/v1',
    timeout: 30000, // 30 seconds
    maxRetries: 3,
  },

  // Real-time configuration
  realtime: {
    stationUpdateInterval: 30000, // 30 seconds
    connectionTimeout: 5000, // 5 seconds
  },

  // Map configuration
  map: {
    defaultZoom: 15,
    maxZoom: 20,
    minZoom: 10,
    defaultRadius: 10, // km
    maxStationsPerRequest: 100,
  },

  // Validation configuration
  validation: {
    maxFileSize: 5 * 1024 * 1024, // 5MB
    allowedImageTypes: ['image/jpeg', 'image/png', 'image/webp'],
    maxImageDimensions: {
      width: 2048,
      height: 2048,
    },
  },

  // Security configuration
  security: {
    maxLoginAttempts: 5,
    lockoutDuration: 15 * 60 * 1000, // 15 minutes
    passwordMinLength: 8,
    sessionTimeout: 24 * 60 * 60 * 1000, // 24 hours
  },
} as const;

/**
 * Type definitions
 */
export type Config = typeof config;
export type Environment = typeof env.NODE_ENV;

/**
 * Helper functions
 */
export const isDevelopment = () => config.env === 'development';
export const isStaging = () => config.env === 'staging';
export const isProduction = () => config.env === 'production';

/**
 * Validate required configuration
 */
export const validateConfig = () => {
  const requiredConfigs = [
    { key: 'DATABASE_URL', value: env.DATABASE_URL },
    { key: 'JWT_SECRET', value: env.JWT_SECRET },
    { key: 'JWT_REFRESH_SECRET', value: env.JWT_REFRESH_SECRET },
  ];

  const missing = requiredConfigs.filter(({ value }) => !value);

  if (missing.length > 0) {
    console.error('❌ Missing required configuration:');
    missing.forEach(({ key }) => {
      console.error(`  ${key}`);
    });
    process.exit(1);
  }

  console.log('✅ Configuration validated successfully');
};
