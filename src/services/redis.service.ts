import { createClient, RedisClientType } from 'redis';
import { config } from '@/config/app.config';
import { logger } from '@/utils/logger';

/**
 * Redis service for caching and session management
 */
class RedisService {
  private static instance: RedisClientType | null = null;
  private static isConnected = false;

  /**
   * Get Redis client instance (singleton)
   */
  public static getInstance(): RedisClientType {
    if (!RedisService.instance) {
      RedisService.instance = createClient({
        url: config.database.redis.url,
        socket: {
          connectTimeout: 5000,
          lazyConnect: true,
        },
      });

      // Set up event listeners
      RedisService.setupEventListeners();
    }

    return RedisService.instance;
  }

  /**
   * Setup Redis event listeners
   */
  private static setupEventListeners(): void {
    const redis = RedisService.instance!;

    redis.on('connect', () => {
      logger.info('Redis client connected');
    });

    redis.on('ready', () => {
      RedisService.isConnected = true;
      logger.info('Redis client ready');
    });

    redis.on('error', (error) => {
      RedisService.isConnected = false;
      logger.error('Redis client error', error);
    });

    redis.on('end', () => {
      RedisService.isConnected = false;
      logger.info('Redis client disconnected');
    });

    redis.on('reconnecting', () => {
      logger.info('Redis client reconnecting');
    });
  }

  /**
   * Connect to Redis
   */
  public static async connect(): Promise<void> {
    try {
      const redis = RedisService.getInstance();
      await redis.connect();
      
      // Test connection
      await redis.ping();
      
      logger.info('Redis connected successfully');
    } catch (error) {
      RedisService.isConnected = false;
      logger.error('Failed to connect to Redis', error);
      throw error;
    }
  }

  /**
   * Disconnect from Redis
   */
  public static async disconnect(): Promise<void> {
    try {
      if (RedisService.instance) {
        await RedisService.instance.quit();
        RedisService.instance = null;
        RedisService.isConnected = false;
        logger.info('Redis disconnected successfully');
      }
    } catch (error) {
      logger.error('Failed to disconnect from Redis', error);
      throw error;
    }
  }

  /**
   * Check if Redis is connected
   */
  public static isHealthy(): boolean {
    return RedisService.isConnected;
  }

  /**
   * Health check
   */
  public static async healthCheck(): Promise<{ status: string; details?: any }> {
    try {
      const redis = RedisService.getInstance();
      
      const start = Date.now();
      const pong = await redis.ping();
      const duration = Date.now() - start;
      
      return {
        status: 'healthy',
        details: {
          connected: true,
          responseTime: `${duration}ms`,
          response: pong,
        },
      };
    } catch (error) {
      return {
        status: 'unhealthy',
        details: {
          connected: false,
          error: error instanceof Error ? error.message : 'Unknown error',
        },
      };
    }
  }

  /**
   * Set a key-value pair with optional TTL
   */
  public static async set(
    key: string,
    value: any,
    ttlSeconds?: number
  ): Promise<void> {
    try {
      const redis = RedisService.getInstance();
      const serializedValue = JSON.stringify(value);
      
      if (ttlSeconds) {
        await redis.setEx(key, ttlSeconds, serializedValue);
      } else {
        await redis.set(key, serializedValue);
      }
      
      logger.debug('Redis SET', { key, ttl: ttlSeconds });
    } catch (error) {
      logger.error('Redis SET failed', { key, error });
      throw error;
    }
  }

  /**
   * Get a value by key
   */
  public static async get<T = any>(key: string): Promise<T | null> {
    try {
      const redis = RedisService.getInstance();
      const value = await redis.get(key);
      
      if (value === null) {
        logger.debug('Redis GET miss', { key });
        return null;
      }
      
      logger.debug('Redis GET hit', { key });
      return JSON.parse(value) as T;
    } catch (error) {
      logger.error('Redis GET failed', { key, error });
      throw error;
    }
  }

  /**
   * Delete a key
   */
  public static async delete(key: string): Promise<boolean> {
    try {
      const redis = RedisService.getInstance();
      const result = await redis.del(key);
      
      logger.debug('Redis DEL', { key, deleted: result > 0 });
      return result > 0;
    } catch (error) {
      logger.error('Redis DEL failed', { key, error });
      throw error;
    }
  }

  /**
   * Check if key exists
   */
  public static async exists(key: string): Promise<boolean> {
    try {
      const redis = RedisService.getInstance();
      const result = await redis.exists(key);
      return result === 1;
    } catch (error) {
      logger.error('Redis EXISTS failed', { key, error });
      throw error;
    }
  }

  /**
   * Set TTL for a key
   */
  public static async expire(key: string, ttlSeconds: number): Promise<boolean> {
    try {
      const redis = RedisService.getInstance();
      const result = await redis.expire(key, ttlSeconds);
      return result;
    } catch (error) {
      logger.error('Redis EXPIRE failed', { key, ttlSeconds, error });
      throw error;
    }
  }

  /**
   * Get TTL for a key
   */
  public static async ttl(key: string): Promise<number> {
    try {
      const redis = RedisService.getInstance();
      return await redis.ttl(key);
    } catch (error) {
      logger.error('Redis TTL failed', { key, error });
      throw error;
    }
  }

  /**
   * Delete keys by pattern
   */
  public static async deletePattern(pattern: string): Promise<number> {
    try {
      const redis = RedisService.getInstance();
      const keys = await redis.keys(pattern);
      
      if (keys.length === 0) {
        return 0;
      }
      
      const result = await redis.del(keys);
      logger.debug('Redis DEL pattern', { pattern, keysDeleted: result });
      return result;
    } catch (error) {
      logger.error('Redis DEL pattern failed', { pattern, error });
      throw error;
    }
  }

  /**
   * Increment a counter
   */
  public static async increment(key: string, by: number = 1): Promise<number> {
    try {
      const redis = RedisService.getInstance();
      return await redis.incrBy(key, by);
    } catch (error) {
      logger.error('Redis INCR failed', { key, by, error });
      throw error;
    }
  }

  /**
   * Add to a set
   */
  public static async addToSet(key: string, ...members: string[]): Promise<number> {
    try {
      const redis = RedisService.getInstance();
      return await redis.sAdd(key, members);
    } catch (error) {
      logger.error('Redis SADD failed', { key, members, error });
      throw error;
    }
  }

  /**
   * Get set members
   */
  public static async getSetMembers(key: string): Promise<string[]> {
    try {
      const redis = RedisService.getInstance();
      return await redis.sMembers(key);
    } catch (error) {
      logger.error('Redis SMEMBERS failed', { key, error });
      throw error;
    }
  }

  /**
   * Check if member exists in set
   */
  public static async isSetMember(key: string, member: string): Promise<boolean> {
    try {
      const redis = RedisService.getInstance();
      return await redis.sIsMember(key, member);
    } catch (error) {
      logger.error('Redis SISMEMBER failed', { key, member, error });
      throw error;
    }
  }

  /**
   * Cache with automatic serialization
   */
  public static async cache<T>(
    key: string,
    fetcher: () => Promise<T>,
    ttlSeconds: number = config.cache.ttl.stations
  ): Promise<T> {
    try {
      // Try to get from cache first
      const cached = await RedisService.get<T>(key);
      if (cached !== null) {
        return cached;
      }

      // Fetch fresh data
      const data = await fetcher();
      
      // Cache the result
      await RedisService.set(key, data, ttlSeconds);
      
      return data;
    } catch (error) {
      logger.error('Redis cache failed', { key, error });
      // If caching fails, still return the fetched data
      return await fetcher();
    }
  }

  /**
   * Flush all data (use with caution)
   */
  public static async flushAll(): Promise<void> {
    if (config.env === 'production') {
      throw new Error('Cannot flush Redis in production');
    }

    try {
      const redis = RedisService.getInstance();
      await redis.flushAll();
      logger.warn('Redis flushed all data');
    } catch (error) {
      logger.error('Redis FLUSHALL failed', error);
      throw error;
    }
  }

  /**
   * Get Redis info
   */
  public static async getInfo(): Promise<any> {
    try {
      const redis = RedisService.getInstance();
      const info = await redis.info();
      return info;
    } catch (error) {
      logger.error('Redis INFO failed', error);
      throw error;
    }
  }
}

// Export singleton instance
export const redis = RedisService.getInstance();
export { RedisService };
