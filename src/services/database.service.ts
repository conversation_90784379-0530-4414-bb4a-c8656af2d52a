import { PrismaClient } from '@prisma/client';
import { config } from '@/config/app.config';
import { logger } from '@/utils/logger';

/**
 * Database service using Prisma
 */
class DatabaseService {
  private static instance: PrismaClient | null = null;
  private static isConnected = false;

  /**
   * Get Prisma client instance (singleton)
   */
  public static getInstance(): PrismaClient {
    if (!DatabaseService.instance) {
      DatabaseService.instance = new PrismaClient({
        log: [
          {
            emit: 'event',
            level: 'query',
          },
          {
            emit: 'event',
            level: 'error',
          },
          {
            emit: 'event',
            level: 'info',
          },
          {
            emit: 'event',
            level: 'warn',
          },
        ],
        errorFormat: 'pretty',
      });

      // Set up logging
      DatabaseService.setupLogging();
    }

    return DatabaseService.instance;
  }

  /**
   * Setup database logging
   */
  private static setupLogging(): void {
    const prisma = DatabaseService.instance!;

    prisma.$on('query', (e) => {
      if (config.env === 'development') {
        logger.debug('Database Query', {
          query: e.query,
          params: e.params,
          duration: `${e.duration}ms`,
          target: e.target,
        });
      }
    });

    prisma.$on('error', (e) => {
      logger.error('Database Error', {
        message: e.message,
        target: e.target,
      });
    });

    prisma.$on('info', (e) => {
      logger.info('Database Info', {
        message: e.message,
        target: e.target,
      });
    });

    prisma.$on('warn', (e) => {
      logger.warn('Database Warning', {
        message: e.message,
        target: e.target,
      });
    });
  }

  /**
   * Connect to database
   */
  public static async connect(): Promise<void> {
    try {
      const prisma = DatabaseService.getInstance();
      
      // Test connection
      await prisma.$connect();
      
      // Run a simple query to verify connection
      await prisma.$queryRaw`SELECT 1`;
      
      DatabaseService.isConnected = true;
      logger.info('Database connected successfully');
      
      // Log database info
      const result = await prisma.$queryRaw<Array<{ version: string }>>`SELECT version()`;
      logger.info('Database version', { version: result[0]?.version });
      
    } catch (error) {
      DatabaseService.isConnected = false;
      logger.error('Failed to connect to database', error);
      throw error;
    }
  }

  /**
   * Disconnect from database
   */
  public static async disconnect(): Promise<void> {
    try {
      if (DatabaseService.instance) {
        await DatabaseService.instance.$disconnect();
        DatabaseService.instance = null;
        DatabaseService.isConnected = false;
        logger.info('Database disconnected successfully');
      }
    } catch (error) {
      logger.error('Failed to disconnect from database', error);
      throw error;
    }
  }

  /**
   * Check if database is connected
   */
  public static isHealthy(): boolean {
    return DatabaseService.isConnected;
  }

  /**
   * Health check
   */
  public static async healthCheck(): Promise<{ status: string; details?: any }> {
    try {
      const prisma = DatabaseService.getInstance();
      
      // Simple query to test connection
      const start = Date.now();
      await prisma.$queryRaw`SELECT 1`;
      const duration = Date.now() - start;
      
      return {
        status: 'healthy',
        details: {
          connected: true,
          responseTime: `${duration}ms`,
        },
      };
    } catch (error) {
      return {
        status: 'unhealthy',
        details: {
          connected: false,
          error: error instanceof Error ? error.message : 'Unknown error',
        },
      };
    }
  }

  /**
   * Run database migrations
   */
  public static async runMigrations(): Promise<void> {
    try {
      logger.info('Running database migrations...');
      
      // Note: In production, migrations should be run separately
      // This is mainly for development convenience
      if (config.env === 'development') {
        const { execSync } = require('child_process');
        execSync('npx prisma migrate deploy', { stdio: 'inherit' });
        logger.info('Database migrations completed');
      }
    } catch (error) {
      logger.error('Failed to run migrations', error);
      throw error;
    }
  }

  /**
   * Seed database with initial data
   */
  public static async seedDatabase(): Promise<void> {
    try {
      logger.info('Seeding database...');
      
      const prisma = DatabaseService.getInstance();
      
      // Create system configurations
      await prisma.systemConfig.upsert({
        where: { key: 'app_version' },
        update: { value: config.app.version },
        create: {
          key: 'app_version',
          value: config.app.version,
          description: 'Current application version',
          category: 'system',
          isPublic: true,
        },
      });

      await prisma.systemConfig.upsert({
        where: { key: 'maintenance_mode' },
        update: {},
        create: {
          key: 'maintenance_mode',
          value: false,
          description: 'Enable/disable maintenance mode',
          category: 'system',
          isPublic: true,
        },
      });

      // Create default API key for development
      if (config.env === 'development') {
        await prisma.apiKey.upsert({
          where: { key: 'dev-api-key-12345' },
          update: {},
          create: {
            name: 'Development API Key',
            key: 'dev-api-key-12345',
            permissions: ['read:stations', 'write:stations'],
            rateLimit: 1000,
          },
        });
      }

      logger.info('Database seeded successfully');
    } catch (error) {
      logger.error('Failed to seed database', error);
      throw error;
    }
  }

  /**
   * Clear all data (for testing)
   */
  public static async clearDatabase(): Promise<void> {
    if (config.env === 'production') {
      throw new Error('Cannot clear database in production');
    }

    try {
      logger.warn('Clearing database...');
      
      const prisma = DatabaseService.getInstance();
      
      // Delete in correct order to avoid foreign key constraints
      await prisma.checkIn.deleteMany();
      await prisma.tripStop.deleteMany();
      await prisma.tripPlan.deleteMany();
      await prisma.chargingConnector.deleteMany();
      await prisma.chargingStation.deleteMany();
      await prisma.vehicle.deleteMany();
      await prisma.refreshToken.deleteMany();
      await prisma.user.deleteMany();
      await prisma.apiKey.deleteMany();
      await prisma.systemConfig.deleteMany();
      
      logger.warn('Database cleared');
    } catch (error) {
      logger.error('Failed to clear database', error);
      throw error;
    }
  }

  /**
   * Get database statistics
   */
  public static async getStats(): Promise<any> {
    try {
      const prisma = DatabaseService.getInstance();
      
      const [
        userCount,
        stationCount,
        connectorCount,
        tripCount,
        checkInCount,
      ] = await Promise.all([
        prisma.user.count(),
        prisma.chargingStation.count(),
        prisma.chargingConnector.count(),
        prisma.tripPlan.count(),
        prisma.checkIn.count(),
      ]);

      return {
        users: userCount,
        stations: stationCount,
        connectors: connectorCount,
        trips: tripCount,
        checkIns: checkInCount,
        timestamp: new Date().toISOString(),
      };
    } catch (error) {
      logger.error('Failed to get database stats', error);
      throw error;
    }
  }

  /**
   * Execute raw SQL query (use with caution)
   */
  public static async executeRaw(query: string, params: any[] = []): Promise<any> {
    try {
      const prisma = DatabaseService.getInstance();
      return await prisma.$queryRawUnsafe(query, ...params);
    } catch (error) {
      logger.error('Failed to execute raw query', { query, params, error });
      throw error;
    }
  }

  /**
   * Transaction wrapper
   */
  public static async transaction<T>(
    fn: (prisma: PrismaClient) => Promise<T>
  ): Promise<T> {
    const prisma = DatabaseService.getInstance();
    return await prisma.$transaction(fn);
  }
}

// Export singleton instance
export const prisma = DatabaseService.getInstance();
export { DatabaseService };
