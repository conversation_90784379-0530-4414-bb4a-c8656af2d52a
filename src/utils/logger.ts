import winston from 'winston';
import { config } from '@/config/app.config';

/**
 * Custom log format
 */
const logFormat = winston.format.combine(
  winston.format.timestamp({
    format: 'YYYY-MM-DD HH:mm:ss',
  }),
  winston.format.errors({ stack: true }),
  winston.format.json(),
  winston.format.printf(({ timestamp, level, message, stack, ...meta }) => {
    let log = `${timestamp} [${level.toUpperCase()}]: ${message}`;
    
    if (Object.keys(meta).length > 0) {
      log += ` ${JSON.stringify(meta)}`;
    }
    
    if (stack) {
      log += `\n${stack}`;
    }
    
    return log;
  })
);

/**
 * Console format for development
 */
const consoleFormat = winston.format.combine(
  winston.format.colorize(),
  winston.format.timestamp({
    format: 'HH:mm:ss',
  }),
  winston.format.printf(({ timestamp, level, message, stack, ...meta }) => {
    let log = `${timestamp} ${level}: ${message}`;
    
    if (Object.keys(meta).length > 0) {
      log += ` ${JSON.stringify(meta, null, 2)}`;
    }
    
    if (stack) {
      log += `\n${stack}`;
    }
    
    return log;
  })
);

/**
 * Create transports based on environment
 */
const createTransports = (): winston.transport[] => {
  const transports: winston.transport[] = [];

  // Console transport (always enabled)
  transports.push(
    new winston.transports.Console({
      format: config.env === 'production' ? logFormat : consoleFormat,
    })
  );

  // File transports (production and staging)
  if (config.env !== 'development') {
    // Error log file
    transports.push(
      new winston.transports.File({
        filename: 'logs/error.log',
        level: 'error',
        format: logFormat,
        maxsize: 5242880, // 5MB
        maxFiles: 5,
      })
    );

    // Combined log file
    transports.push(
      new winston.transports.File({
        filename: 'logs/combined.log',
        format: logFormat,
        maxsize: 5242880, // 5MB
        maxFiles: 5,
      })
    );
  }

  return transports;
};

/**
 * Winston logger instance
 */
export const logger = winston.createLogger({
  level: config.logging.level,
  format: logFormat,
  defaultMeta: {
    service: config.app.name,
    version: config.app.version,
    environment: config.env,
  },
  transports: createTransports(),
  exitOnError: false,
});

/**
 * Request logger middleware
 */
export const requestLogger = (req: any, res: any, next: any) => {
  const start = Date.now();
  
  res.on('finish', () => {
    const duration = Date.now() - start;
    const logData = {
      method: req.method,
      url: req.url,
      statusCode: res.statusCode,
      duration: `${duration}ms`,
      userAgent: req.get('User-Agent'),
      ip: req.ip,
      requestId: req.id,
    };

    if (res.statusCode >= 400) {
      logger.warn('HTTP Request', logData);
    } else {
      logger.info('HTTP Request', logData);
    }
  });

  next();
};

/**
 * Database logger
 */
export const dbLogger = {
  query: (query: string, params?: any[]) => {
    logger.debug('Database Query', {
      query: query.replace(/\s+/g, ' ').trim(),
      params,
    });
  },
  
  error: (error: Error, query?: string) => {
    logger.error('Database Error', {
      error: error.message,
      stack: error.stack,
      query,
    });
  },
  
  connection: (event: string, details?: any) => {
    logger.info(`Database ${event}`, details);
  },
};

/**
 * Performance logger
 */
export const performanceLogger = {
  start: (operation: string) => {
    const startTime = process.hrtime.bigint();
    
    return {
      end: (metadata?: any) => {
        const endTime = process.hrtime.bigint();
        const duration = Number(endTime - startTime) / 1000000; // Convert to milliseconds
        
        logger.debug('Performance', {
          operation,
          duration: `${duration.toFixed(2)}ms`,
          ...metadata,
        });
      },
    };
  },
};

/**
 * Security logger
 */
export const securityLogger = {
  authAttempt: (email: string, success: boolean, ip: string) => {
    logger.info('Authentication Attempt', {
      email,
      success,
      ip,
      timestamp: new Date().toISOString(),
    });
  },
  
  authFailure: (email: string, reason: string, ip: string) => {
    logger.warn('Authentication Failure', {
      email,
      reason,
      ip,
      timestamp: new Date().toISOString(),
    });
  },
  
  suspiciousActivity: (activity: string, details: any) => {
    logger.warn('Suspicious Activity', {
      activity,
      ...details,
      timestamp: new Date().toISOString(),
    });
  },
  
  dataAccess: (userId: string, resource: string, action: string) => {
    logger.info('Data Access', {
      userId,
      resource,
      action,
      timestamp: new Date().toISOString(),
    });
  },
};

/**
 * API logger
 */
export const apiLogger = {
  request: (method: string, url: string, body?: any, headers?: any) => {
    logger.debug('API Request', {
      method,
      url,
      body: body ? JSON.stringify(body) : undefined,
      headers,
    });
  },
  
  response: (method: string, url: string, statusCode: number, responseTime: number) => {
    logger.debug('API Response', {
      method,
      url,
      statusCode,
      responseTime: `${responseTime}ms`,
    });
  },
  
  error: (method: string, url: string, error: Error) => {
    logger.error('API Error', {
      method,
      url,
      error: error.message,
      stack: error.stack,
    });
  },
};

/**
 * Business logic logger
 */
export const businessLogger = {
  userAction: (userId: string, action: string, details?: any) => {
    logger.info('User Action', {
      userId,
      action,
      ...details,
      timestamp: new Date().toISOString(),
    });
  },
  
  systemEvent: (event: string, details?: any) => {
    logger.info('System Event', {
      event,
      ...details,
      timestamp: new Date().toISOString(),
    });
  },
  
  dataChange: (entity: string, entityId: string, changes: any, userId?: string) => {
    logger.info('Data Change', {
      entity,
      entityId,
      changes,
      userId,
      timestamp: new Date().toISOString(),
    });
  },
};

/**
 * Error logger with context
 */
export const errorLogger = {
  log: (error: Error, context?: any) => {
    logger.error('Application Error', {
      message: error.message,
      stack: error.stack,
      name: error.name,
      ...context,
    });
  },
  
  validation: (field: string, value: any, rule: string) => {
    logger.warn('Validation Error', {
      field,
      value,
      rule,
    });
  },
  
  external: (service: string, error: Error, request?: any) => {
    logger.error('External Service Error', {
      service,
      error: error.message,
      request,
    });
  },
};

/**
 * Structured logging helpers
 */
export const createLogger = (module: string) => {
  return {
    debug: (message: string, meta?: any) => logger.debug(message, { module, ...meta }),
    info: (message: string, meta?: any) => logger.info(message, { module, ...meta }),
    warn: (message: string, meta?: any) => logger.warn(message, { module, ...meta }),
    error: (message: string, meta?: any) => logger.error(message, { module, ...meta }),
  };
};

// Export default logger
export default logger;
