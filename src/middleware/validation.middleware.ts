import { Request, Response, NextFunction } from 'express';
import { validationResult, Validation<PERSON>hain } from 'express-validator';
import { ValidationError } from './error.middleware';

/**
 * Validation middleware
 * Checks for validation errors and returns formatted error response
 */
export const validationMiddleware = (
  req: Request,
  res: Response,
  next: NextFunction
): void => {
  const errors = validationResult(req);

  if (!errors.isEmpty()) {
    const formattedErrors = errors.array().map(error => ({
      field: error.type === 'field' ? (error as any).path : 'unknown',
      message: error.msg,
      value: error.type === 'field' ? (error as any).value : undefined,
    }));

    throw new ValidationError('Validation failed', formattedErrors);
  }

  next();
};

/**
 * Validation chain runner
 * Runs validation chains and handles errors
 */
export const validate = (validations: ValidationChain[]) => {
  return async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    // Run all validations
    await Promise.all(validations.map(validation => validation.run(req)));

    // Check for errors
    validationMiddleware(req, res, next);
  };
};

/**
 * Custom validation helpers
 */
export const customValidators = {
  /**
   * Check if value is a valid ObjectId
   */
  isObjectId: (value: string): boolean => {
    return /^[0-9a-fA-F]{24}$/.test(value);
  },

  /**
   * Check if value is a valid UUID
   */
  isUUID: (value: string): boolean => {
    return /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i.test(value);
  },

  /**
   * Check if value is a valid latitude
   */
  isLatitude: (value: number): boolean => {
    return typeof value === 'number' && value >= -90 && value <= 90;
  },

  /**
   * Check if value is a valid longitude
   */
  isLongitude: (value: number): boolean => {
    return typeof value === 'number' && value >= -180 && value <= 180;
  },

  /**
   * Check if value is a valid phone number (Vietnamese format)
   */
  isVietnamesePhone: (value: string): boolean => {
    return /^(\+84|84|0)(3|5|7|8|9)[0-9]{8}$/.test(value);
  },

  /**
   * Check if value is a strong password
   */
  isStrongPassword: (value: string): boolean => {
    // At least 8 characters, 1 uppercase, 1 lowercase, 1 number
    return /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)[a-zA-Z\d@$!%*?&]{8,}$/.test(value);
  },

  /**
   * Check if value is a valid VinFast vehicle model
   */
  isVinFastModel: (value: string): boolean => {
    const validModels = ['VF5', 'VF6', 'VF7', 'VF8', 'VF9', 'VFe34', 'VFe35', 'VFe36'];
    return validModels.includes(value);
  },

  /**
   * Check if value is a valid connector type
   */
  isConnectorType: (value: string): boolean => {
    const validTypes = ['AC', 'DC', 'CCS2', 'CHAdeMO', 'Type2'];
    return validTypes.includes(value);
  },

  /**
   * Check if value is a valid station status
   */
  isStationStatus: (value: string): boolean => {
    const validStatuses = ['active', 'inactive', 'maintenance', 'offline'];
    return validStatuses.includes(value);
  },

  /**
   * Check if value is a valid file type
   */
  isValidImageType: (mimetype: string): boolean => {
    const validTypes = ['image/jpeg', 'image/png', 'image/webp'];
    return validTypes.includes(mimetype);
  },

  /**
   * Check if value is within file size limit
   */
  isValidFileSize: (size: number, maxSize: number = 5 * 1024 * 1024): boolean => {
    return size <= maxSize;
  },
};

/**
 * Sanitization helpers
 */
export const sanitizers = {
  /**
   * Trim and normalize string
   */
  normalizeString: (value: string): string => {
    return value.trim().replace(/\s+/g, ' ');
  },

  /**
   * Normalize phone number to international format
   */
  normalizePhone: (value: string): string => {
    // Remove all non-digit characters
    const digits = value.replace(/\D/g, '');
    
    // Convert to +84 format
    if (digits.startsWith('0')) {
      return '+84' + digits.slice(1);
    } else if (digits.startsWith('84')) {
      return '+' + digits;
    } else if (digits.startsWith('+84')) {
      return digits;
    }
    
    return value;
  },

  /**
   * Normalize email to lowercase
   */
  normalizeEmail: (value: string): string => {
    return value.toLowerCase().trim();
  },

  /**
   * Escape HTML characters
   */
  escapeHtml: (value: string): string => {
    return value
      .replace(/&/g, '&amp;')
      .replace(/</g, '&lt;')
      .replace(/>/g, '&gt;')
      .replace(/"/g, '&quot;')
      .replace(/'/g, '&#x27;');
  },
};

/**
 * Common validation schemas
 */
export const commonValidations = {
  pagination: {
    page: {
      in: ['query'],
      isInt: {
        options: { min: 1 },
        errorMessage: 'Page must be a positive integer',
      },
      toInt: true,
      optional: true,
    },
    limit: {
      in: ['query'],
      isInt: {
        options: { min: 1, max: 100 },
        errorMessage: 'Limit must be between 1 and 100',
      },
      toInt: true,
      optional: true,
    },
  },

  location: {
    latitude: {
      isFloat: {
        options: { min: -90, max: 90 },
        errorMessage: 'Latitude must be between -90 and 90',
      },
      toFloat: true,
    },
    longitude: {
      isFloat: {
        options: { min: -180, max: 180 },
        errorMessage: 'Longitude must be between -180 and 180',
      },
      toFloat: true,
    },
  },

  search: {
    query: {
      in: ['query'],
      isString: true,
      trim: true,
      isLength: {
        options: { min: 1, max: 100 },
        errorMessage: 'Search query must be between 1 and 100 characters',
      },
      optional: true,
    },
    radius: {
      in: ['query'],
      isFloat: {
        options: { min: 0.1, max: 100 },
        errorMessage: 'Radius must be between 0.1 and 100 km',
      },
      toFloat: true,
      optional: true,
    },
  },
};
