import { Request, Response, NextFunction } from 'express';
import { logger } from '@/utils/logger';

/**
 * 404 Not Found middleware
 * Handles requests to non-existent routes
 */
export const notFoundHandler = (
  req: Request,
  res: Response,
  next: NextFunction
): void => {
  // Log the 404 request
  logger.warn('Route not found', {
    method: req.method,
    url: req.url,
    ip: req.ip,
    userAgent: req.get('User-Agent'),
    requestId: req.id,
  });

  // Send 404 response
  res.status(404).json({
    success: false,
    error: {
      message: 'Route not found',
      code: 'ROUTE_NOT_FOUND',
      path: req.url,
      method: req.method,
      timestamp: new Date().toISOString(),
      requestId: req.id,
    },
  });
};
